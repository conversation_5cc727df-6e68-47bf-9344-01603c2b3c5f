<?php

namespace App\Filament\Setting\Resources\EventManagerResource\Pages;

use App\Filament\Setting\Resources\EventManagerResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditEventManager extends EditRecord
{
    protected static string $resource = EventManagerResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
