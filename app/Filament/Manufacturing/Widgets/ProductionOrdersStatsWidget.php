<?php

namespace App\Filament\Manufacturing\Widgets;

use App\Models\ProductionOrder;
use Filament\Widgets\ChartWidget;

class ProductionOrdersStatsWidget extends ChartWidget
{
    protected static ?string $heading = 'Order Produksi berdasarkan Status';

    protected function getData(): array
    {
        $statuses = ['planned', 'released', 'in_progress', 'completed', 'cancelled', 'on_hold'];
        $data = [];
        $labels = [];
        $colors = [];

        foreach ($statuses as $status) {
            $count = ProductionOrder::where('status', $status)->count();
            if ($count > 0) {
                $data[] = $count;
                $labels[] = ucfirst(str_replace('_', ' ', $status));
                $colors[] = $this->getStatusColor($status);
            }
        }

        return [
            'datasets' => [
                [
                    'label' => 'Production Orders',
                    'data' => $data,
                    'backgroundColor' => $colors,
                    'borderColor' => $colors,
                    'borderWidth' => 1,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'doughnut';
    }

    protected function getStatusColor(string $status): string
    {
        return match ($status) {
            'planned' => '#6B7280',
            'released' => '#3B82F6',
            'in_progress' => '#F59E0B',
            'completed' => '#10B981',
            'cancelled' => '#EF4444',
            'on_hold' => '#8B5CF6',
            default => '#6B7280',
        };
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'bottom',
                ],
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
        ];
    }
}
