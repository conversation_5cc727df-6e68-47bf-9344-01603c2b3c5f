<?php

namespace App\Filament\Accounting\Resources\BankReconciliationResource\Pages;

use App\Filament\Accounting\Resources\BankReconciliationResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewBankReconciliation extends ViewRecord
{
    protected static string $resource = BankReconciliationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->label('Edit Rekonsiliasi')
                ->icon('heroicon-o-pencil'),
            Actions\Action::make('reconcile')
                ->label('Proses Rekonsiliasi')
                ->icon('heroicon-o-scale')
                ->color('info')
                ->visible(fn () => $this->record->status === 'draft' || $this->record->status === 'in_progress')
                ->url(fn () => static::getResource()::getUrl('reconcile', ['record' => $this->record])),
            Actions\DeleteAction::make()
                ->label('Hapus Rekonsiliasi')
                ->icon('heroicon-o-trash'),
        ];
    }
}
