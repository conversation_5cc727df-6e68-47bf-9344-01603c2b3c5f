<?php

namespace App\Filament\Resources\PayrollTransactionResource\Pages;

use App\Filament\Resources\PayrollTransactionResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;

class ViewPayrollTransaction extends ViewRecord
{
    protected static string $resource = PayrollTransactionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('approve')
                ->label('Approve')
                ->icon('heroicon-o-check')
                ->color('success')
                ->visible(fn() => $this->record->status === 'draft')
                ->requiresConfirmation()
                ->action(fn() => $this->record->approve(auth()->id())),

            Actions\Action::make('mark_paid')
                ->label('Mark as Paid')
                ->icon('heroicon-o-banknotes')
                ->color('primary')
                ->visible(fn() => $this->record->status === 'approved')
                ->requiresConfirmation()
                ->action(fn() => $this->record->markAsPaid()),

            Actions\Action::make('print')
                ->label('Print Slip Gaji')
                ->icon('heroicon-o-printer')
                ->color('gray')
                ->url(fn() => route('payroll.slip', $this->record))
                ->openUrlInNewTab(),

            Actions\Action::make('manage_tolerance')
                ->label('Kelola Toleransi')
                ->icon('heroicon-o-clock')
                ->color('warning')
                ->visible(fn() => $this->record->status === 'draft' && $this->hasLateAttendance())
                ->modalHeading('Kelola Toleransi Keterlambatan')
                ->modalDescription('Berikan atau hapus toleransi untuk keterlambatan karyawan dalam periode ini.')
                ->modalSubmitActionLabel('Simpan')
                ->form([
                    \Filament\Forms\Components\Select::make('absensi_id')
                        ->label('Pilih Absensi Terlambat')
                        ->options(fn() => $this->getLateAttendanceOptions())
                        ->required()
                        ->reactive()
                        ->afterStateUpdated(fn($state, $set) => $this->loadAbsensiData($state, $set)),

                    \Filament\Forms\Components\Placeholder::make('absensi_info')
                        ->label('Informasi Absensi')
                        ->content(fn($get) => $this->getAbsensiInfo($get('absensi_id')))
                        ->visible(fn($get) => $get('absensi_id')),

                    \Filament\Forms\Components\Toggle::make('give_tolerance')
                        ->label('Berikan Toleransi')
                        ->helperText('Aktifkan untuk memberikan toleransi keterlambatan')
                        ->reactive()
                        ->visible(fn($get) => $get('absensi_id')),

                    \Filament\Forms\Components\Textarea::make('tolerance_reason')
                        ->label('Alasan Toleransi')
                        ->placeholder('Masukkan alasan pemberian toleransi...')
                        ->required()
                        ->visible(fn($get) => $get('give_tolerance'))
                        ->rows(3),
                ])
                ->action(function (array $data) {
                    $this->handleToleranceAction($data);
                }),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Informasi Payroll')
                    ->schema([
                        Infolists\Components\TextEntry::make('no_payroll')
                            ->label('No. Payroll'),
                        Infolists\Components\TextEntry::make('payrollPeriod.nama_periode')
                            ->label('Periode'),
                        Infolists\Components\TextEntry::make('karyawan.nama_lengkap')
                            ->label('Karyawan'),
                        Infolists\Components\TextEntry::make('karyawan.jabatan.nama_jabatan')
                            ->label('Jabatan'),
                        Infolists\Components\TextEntry::make('status')
                            ->badge()
                            ->color(fn(string $state): string => match ($state) {
                                'draft' => 'gray',
                                'approved' => 'success',
                                'paid' => 'primary',
                                'cancelled' => 'danger',
                            }),
                    ])->columns(3),

                Infolists\Components\Section::make('Komponen Gaji Pokok')
                    ->schema([
                        Infolists\Components\TextEntry::make('gaji_pokok')
                            ->label('Gaji Pokok')
                            ->money('IDR'),
                        Infolists\Components\TextEntry::make('tunjangan_jabatan')
                            ->label('Tunjangan Jabatan')
                            ->money('IDR'),
                        Infolists\Components\TextEntry::make('tunjangan_umum')
                            ->label('Tunjangan Umum')
                            ->money('IDR'),
                        Infolists\Components\TextEntry::make('tunjangan_sembako')
                            ->label('Tunjangan Sembako')
                            ->money('IDR'),
                        Infolists\Components\TextEntry::make('insentif')
                            ->label('Insentif')
                            ->money('IDR'),
                    ])->columns(3),

                Infolists\Components\Section::make('Komponen Lembur')
                    ->schema([
                        Infolists\Components\TextEntry::make('lembur_biasa')
                            ->label('Lembur Biasa')
                            ->money('IDR'),
                        Infolists\Components\TextEntry::make('lembur_tanggal_merah')
                            ->label('Lembur Tanggal Merah')
                            ->money('IDR'),
                        Infolists\Components\TextEntry::make('lembur_tambah_hk')
                            ->label('Lembur Tambah HK')
                            ->money('IDR'),
                    ])->columns(3),

                Infolists\Components\Section::make('Penerimaan Lainnya')
                    ->schema([
                        Infolists\Components\TextEntry::make('kekurangan_gaji_bulan_sebelum')
                            ->label('Kekurangan Gaji Bulan Sebelum')
                            ->money('IDR'),
                        Infolists\Components\TextEntry::make('claim_sakit_dengan_surat')
                            ->label('Claim Sakit Dengan Surat')
                            ->money('IDR'),
                        Infolists\Components\TextEntry::make('pesangon')
                            ->label('Pesangon')
                            ->money('IDR'),
                        Infolists\Components\TextEntry::make('total_gaji_kotor')
                            ->label('Total Gaji Kotor')
                            ->money('IDR')
                            ->size('lg')
                            ->weight('bold')
                            ->color('success'),
                    ])->columns(2),

                Infolists\Components\Section::make('Potongan Wajib')
                    ->schema([
                        Infolists\Components\TextEntry::make('potongan_bpjs_kesehatan')
                            ->label('BPJS Kesehatan')
                            ->money('IDR'),
                        Infolists\Components\TextEntry::make('potongan_bpjs_tk')
                            ->label('BPJS Tenaga Kerja')
                            ->money('IDR'),
                        Infolists\Components\TextEntry::make('potongan_keterlambatan')
                            ->label('Keterlambatan')
                            ->money('IDR'),
                        Infolists\Components\TextEntry::make('potongan_pelanggaran')
                            ->label('Pelanggaran')
                            ->money('IDR'),
                    ])->columns(2),

                Infolists\Components\Section::make('Potongan Karyawan')
                    ->schema([
                        Infolists\Components\TextEntry::make('total_potongan_kasir')
                            ->label('Potongan Kasir')
                            ->money('IDR')
                            ->state(function ($record) {
                                return $record->payrollDeductions()->where('jenis_potongan', 'kasir')->sum('nominal');
                            }),
                        Infolists\Components\TextEntry::make('total_potongan_stok_opname')
                            ->label('Potongan Stok Opname')
                            ->money('IDR')
                            ->state(function ($record) {
                                return $record->payrollDeductions()->where('jenis_potongan', 'stok_opname')->sum('nominal');
                            }),
                        Infolists\Components\TextEntry::make('total_potongan_retur')
                            ->label('Potongan Retur')
                            ->money('IDR')
                            ->state(function ($record) {
                                return $record->payrollDeductions()->where('jenis_potongan', 'retur')->sum('nominal');
                            }),
                        Infolists\Components\TextEntry::make('total_potongan_kasbon')
                            ->label('Potongan Kasbon')
                            ->money('IDR')
                            ->state(function ($record) {
                                return $record->payrollDeductions()->where('jenis_potongan', 'kasbon')->sum('nominal');
                            }),
                    ])->columns(2),

                Infolists\Components\Section::make('Potongan Absensi')
                    ->schema([
                        Infolists\Components\TextEntry::make('total_sakit_tanpa_surat')
                            ->label('Sakit Tanpa Surat')
                            ->money('IDR')
                            ->state(function ($record) {
                                return $record->payrollDeductions()->where('jenis_potongan', 'sakit_tanpa_surat')->sum('nominal');
                            }),
                        Infolists\Components\TextEntry::make('total_alpha')
                            ->label('Alpha/Tidak Hadir')
                            ->money('IDR')
                            ->state(function ($record) {
                                return $record->payrollDeductions()->where('jenis_potongan', 'alpha')->sum('nominal');
                            }),
                        Infolists\Components\TextEntry::make('total_cuti_melebihi_kuota')
                            ->label('Cuti Melebihi Kuota')
                            ->money('IDR')
                            ->state(function ($record) {
                                return $record->payrollDeductions()->where('jenis_potongan', 'cuti_melebihi_kuota')->sum('nominal');
                            }),
                        Infolists\Components\TextEntry::make('total_tidak_absen_keluar')
                            ->label('Tidak Absen Keluar / Pulang')
                            ->money('IDR')
                            ->state(function ($record) {
                                return $record->payrollDeductions()->where('jenis_potongan', 'tidak_absen_keluar')->sum('nominal');
                            }),
                        Infolists\Components\TextEntry::make('potongan_lainnya_sisa')
                            ->label('Potongan Lainnya')
                            ->money('IDR')
                            ->state(function ($record) {
                                $totalKaryawan = $record->payrollDeductions()
                                    ->whereIn('jenis_potongan', ['kasir', 'stok_opname', 'retur', 'kasbon'])
                                    ->sum('nominal');
                                $totalAbsensi = $record->payrollDeductions()
                                    ->whereIn('jenis_potongan', ['sakit_tanpa_surat', 'alpha', 'cuti_melebihi_kuota'])
                                    ->sum('nominal');
                                return $record->potongan_lainnya - $totalKaryawan - $totalAbsensi;
                            }),
                    ])->columns(2),

                Infolists\Components\Section::make('Total Potongan')
                    ->schema([
                        Infolists\Components\TextEntry::make('total_potongan')
                            ->label('Total Potongan')
                            ->money('IDR')
                            ->size('lg')
                            ->weight('bold')
                            ->color('danger'),
                    ])->columns(1),

                Infolists\Components\Section::make('Take Home Pay')
                    ->schema([
                        Infolists\Components\TextEntry::make('take_home_pay')
                            ->label('Take Home Pay')
                            ->money('IDR')
                            ->size('lg')
                            ->weight('bold')
                            ->color('success'),
                    ])->columns(1),

                Infolists\Components\Section::make('Data Absensi & Pelanggaran')
                    ->schema([
                        Infolists\Components\TextEntry::make('total_hari_kerja')
                            ->label('Total Hari Kerja'),
                        Infolists\Components\TextEntry::make('total_hari_hadir')
                            ->label('Total Hari Hadir'),
                        Infolists\Components\TextEntry::make('total_menit_terlambat')
                            ->label('Total Menit Terlambat'),
                        Infolists\Components\TextEntry::make('total_pelanggaran')
                            ->label('Total Pelanggaran'),
                        Infolists\Components\TextEntry::make('sakit_dengan_surat')
                            ->label('Sakit dengan Surat')
                            ->suffix(' hari'),
                        Infolists\Components\TextEntry::make('sakit_tanpa_surat')
                            ->label('Sakit tanpa Surat')
                            ->suffix(' hari'),
                        Infolists\Components\TextEntry::make('izin')
                            ->label('Izin')
                            ->suffix(' hari'),
                        Infolists\Components\TextEntry::make('ambil_cuti')
                            ->label('Ambil Cuti')
                            ->suffix(' hari'),
                        Infolists\Components\TextEntry::make('sisa_cuti')
                            ->label('Sisa Cuti')
                            ->suffix(' hari')
                            ->color(fn($state) => $state <= 3 ? 'warning' : 'success'),
                    ])->columns(3),

                Infolists\Components\Section::make('Detail Potongan Karyawan')
                    ->schema([
                        Infolists\Components\RepeatableEntry::make('payrollDeductions')
                            ->label('')
                            ->schema([
                                Infolists\Components\TextEntry::make('jenis_label')
                                    ->label('Jenis Potongan'),
                                Infolists\Components\TextEntry::make('deskripsi')
                                    ->label('Deskripsi'),
                                Infolists\Components\TextEntry::make('nominal')
                                    ->label('Nominal')
                                    ->money('IDR'),
                                Infolists\Components\TextEntry::make('tanggal_kejadian')
                                    ->label('Tanggal')
                                    ->date('d M Y'),
                            ])
                            ->columns(4)
                            ->state(function ($record) {
                                return $record->payrollDeductions()
                                    ->whereIn('jenis_potongan', ['kasir', 'stok_opname', 'retur', 'kasbon', 'sakit_tanpa_surat', 'alpha', 'cuti_melebihi_kuota'])
                                    ->get();
                            }),
                    ])
                    ->collapsible()
                    ->collapsed(),
            ]);
    }

    /**
     * Check if there are late attendance records
     */
    protected function hasLateAttendance(): bool
    {
        return $this->record->lateAbsensiRecords()->exists();
    }

    /**
     * Get options for late attendance dropdown
     */
    protected function getLateAttendanceOptions(): array
    {
        return $this->record->absensiRecords()
            ->where('status', 'terlambat')
            ->get()
            ->mapWithKeys(function ($absensi) {
                $date = $absensi->tanggal_absensi->format('d M Y');
                $time = $absensi->waktu_masuk ? $absensi->waktu_masuk->format('H:i') : '-';
                $tolerance = $absensi->is_tolerance_given ? ' (Sudah diberi toleransi)' : '';

                return [$absensi->id => "{$date} - {$time}{$tolerance}"];
            })
            ->toArray();
    }

    /**
     * Load absensi data for selected record
     */
    protected function loadAbsensiData($absensiId, $set)
    {
        if (!$absensiId) return;

        $absensi = \App\Models\Absensi::find($absensiId);
        if ($absensi) {
            $set('give_tolerance', $absensi->is_tolerance_given);
            $set('tolerance_reason', $absensi->tolerance_reason);
        }
    }

    /**
     * Get absensi information for display
     */
    protected function getAbsensiInfo($absensiId): string
    {
        if (!$absensiId) return '';

        $absensi = \App\Models\Absensi::with(['jadwal.shift', 'toleranceApprovedBy'])->find($absensiId);
        if (!$absensi) return 'Data absensi tidak ditemukan';

        $info = "Tanggal: " . $absensi->tanggal_absensi->format('d M Y') . "\n";
        $info .= "Waktu Masuk: " . ($absensi->waktu_masuk ? $absensi->waktu_masuk->format('H:i') : '-') . "\n";
        $info .= "Status: " . ucfirst($absensi->status) . "\n";

        if ($absensi->jadwal && $absensi->jadwal->shift) {
            $info .= "Shift: " . $absensi->jadwal->shift->nama_shift . "\n";
            $info .= "Jam Kerja: " . $absensi->jadwal->shift->waktu_mulai . " - " . $absensi->jadwal->shift->waktu_selesai . "\n";
        }

        if ($absensi->is_tolerance_given) {
            $info .= "\n--- TOLERANSI SUDAH DIBERIKAN ---\n";
            $info .= "Alasan: " . $absensi->tolerance_reason . "\n";
            $info .= "Oleh: " . ($absensi->toleranceApprovedBy->name ?? 'Unknown') . "\n";
            $info .= "Pada: " . $absensi->tolerance_approved_at->format('d M Y H:i') . "\n";
        }

        return $info;
    }

    /**
     * Handle tolerance action
     */
    protected function handleToleranceAction(array $data)
    {
        $absensi = \App\Models\Absensi::find($data['absensi_id']);
        if (!$absensi) {
            \Filament\Notifications\Notification::make()
                ->title('Error')
                ->body('Data absensi tidak ditemukan.')
                ->danger()
                ->send();
            return;
        }

        if ($data['give_tolerance']) {
            // Give tolerance
            $absensi->giveTolerance($data['tolerance_reason'], auth()->id());

            \Filament\Notifications\Notification::make()
                ->title('Toleransi Berhasil Diberikan')
                ->body("Toleransi keterlambatan untuk {$absensi->karyawan->nama_lengkap} telah diberikan.")
                ->success()
                ->send();
        } else {
            // Remove tolerance
            $absensi->removeTolerance();

            \Filament\Notifications\Notification::make()
                ->title('Toleransi Berhasil Dihapus')
                ->body("Toleransi keterlambatan untuk {$absensi->karyawan->nama_lengkap} telah dihapus.")
                ->success()
                ->send();
        }

        // Recalculate lateness deductions for this payroll
        $payrollService = new \App\Services\PayrollService();
        $payrollService->recalculateLatenessDeductions($this->record);

        \Filament\Notifications\Notification::make()
            ->title('Potongan Keterlambatan Diperbarui')
            ->body('Potongan keterlambatan telah dihitung ulang berdasarkan toleransi yang diberikan.')
            ->info()
            ->send();

        // Refresh the page to update data
        $this->redirect(request()->header('Referer'));
    }
}
