<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class ShopFloorControl extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'control_number',
        'production_order_id',
        'workstation_id',
        'workstation_name',
        'status',
        'start_time',
        'end_time',
        'planned_duration_minutes',
        'actual_duration_minutes',
        'planned_quantity',
        'actual_quantity',
        'good_quantity',
        'defect_quantity',
        'efficiency_percentage',
        'notes',
        'downtime_logs',
        'quality_checks',
        'supervisor_id',
        'created_by',
    ];

    protected $casts = [
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'planned_quantity' => 'decimal:2',
        'actual_quantity' => 'decimal:2',
        'good_quantity' => 'decimal:2',
        'defect_quantity' => 'decimal:2',
        'efficiency_percentage' => 'decimal:2',
        'downtime_logs' => 'array',
        'quality_checks' => 'array',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->control_number)) {
                $model->control_number = static::generateControlNumber();
            }
        });
    }

    public static function generateControlNumber(): string
    {
        $prefix = 'SFC';
        $date = now()->format('Ymd');
        $lastNumber = static::whereDate('created_at', today())
            ->where('control_number', 'like', $prefix . $date . '%')
            ->count() + 1;

        return $prefix . $date . str_pad($lastNumber, 4, '0', STR_PAD_LEFT);
    }

    // Relationships
    public function productionOrder(): BelongsTo
    {
        return $this->belongsTo(ProductionOrder::class);
    }

    public function workstation(): BelongsTo
    {
        return $this->belongsTo(User::class, 'workstation_id');
    }

    public function supervisor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'supervisor_id');
    }

    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->whereIn('status', ['waiting', 'in_progress', 'paused']);
    }

    public function scopeInProgress($query)
    {
        return $query->where('status', 'in_progress');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    // Methods
    public function start()
    {
        $this->update([
            'status' => 'in_progress',
            'start_time' => now(),
        ]);
    }

    public function pause()
    {
        $this->update([
            'status' => 'paused',
        ]);
    }

    public function resume()
    {
        $this->update([
            'status' => 'in_progress',
        ]);
    }

    public function complete()
    {
        $endTime = now();
        $actualDuration = $this->start_time ? $this->start_time->diffInMinutes($endTime) : 0;

        $this->update([
            'status' => 'completed',
            'end_time' => $endTime,
            'actual_duration_minutes' => $actualDuration,
        ]);

        $this->calculateEfficiency();
    }

    public function stop($reason = null)
    {
        $this->update([
            'status' => 'stopped',
            'end_time' => now(),
            'notes' => $reason ? "Stopped: {$reason}" : 'Stopped',
        ]);
    }

    public function calculateEfficiency()
    {
        if ($this->planned_duration_minutes && $this->actual_duration_minutes) {
            $timeEfficiency = ($this->planned_duration_minutes / $this->actual_duration_minutes) * 100;
            $quantityEfficiency = $this->planned_quantity && $this->actual_quantity
                ? ($this->actual_quantity / $this->planned_quantity) * 100
                : 100;

            $this->efficiency_percentage = min(($timeEfficiency + $quantityEfficiency) / 2, 100);
            $this->save();
        }
    }

    public function addDowntime($reason, $duration_minutes)
    {
        $downtimes = $this->downtime_logs ?? [];
        $downtimes[] = [
            'reason' => $reason,
            'duration_minutes' => $duration_minutes,
            'recorded_at' => now()->toISOString(),
        ];

        $this->update(['downtime_logs' => $downtimes]);
    }

    public function addQualityCheck($checkpoint, $result, $notes = null)
    {
        $checks = $this->quality_checks ?? [];
        $checks[] = [
            'checkpoint' => $checkpoint,
            'result' => $result,
            'notes' => $notes,
            'checked_at' => now()->toISOString(),
        ];

        $this->update(['quality_checks' => $checks]);
    }
}
