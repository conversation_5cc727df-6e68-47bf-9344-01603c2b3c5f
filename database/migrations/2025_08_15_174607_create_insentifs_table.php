<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('insentifs', function (Blueprint $table) {
            $table->id(); // Primary key yang hilang
            $table->foreignId('karyawan_id')->constrained('karyawan')->onDelete('cascade');
            $table->string('jenis_insentif'); // Jenis insentif (Kinerja, Penjualan, Kehadiran, dll)
            $table->decimal('nominal', 15, 2); // Jumlah insentif
            $table->date('tanggal'); // Tanggal pemberian insentif
            $table->string('periode_payroll')->nullable(); // Periode payroll yang akan dimasukkan
            $table->text('keterangan')->nullable(); // Keterangan tambahan
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->foreignId('created_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps(); // Tambahkan timestamps
            $table->softDeletes();

            // Index untuk performa
            $table->index(['karyawan_id', 'tanggal']);
            $table->index(['periode_payroll', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('insentifs');
    }
};
