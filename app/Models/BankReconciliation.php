<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class BankReconciliation extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'bank_reconciliations';

    protected $fillable = [
        'reconciliation_number',
        'account_id',
        'reconciliation_date',
        'statement_date',
        'opening_balance',
        'closing_balance',
        'statement_reference',
        'status',
        'reconciled_by',
        'reconciled_at',
        'notes',
        'created_by',
    ];

    protected $dates = ['deleted_at', 'reconciliation_date', 'statement_date', 'reconciled_at'];

    protected $casts = [
        'reconciliation_date' => 'date',
        'statement_date' => 'date',
        'opening_balance' => 'decimal:2',
        'closing_balance' => 'decimal:2',
        'reconciled_at' => 'datetime',
    ];

    // Relationships
    public function account()
    {
        return $this->belongsTo(Akun::class, 'account_id');
    }

    public function reconciledBy()
    {
        return $this->belongsTo(User::class, 'reconciled_by');
    }

    public function createdBy()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function items()
    {
        return $this->hasMany(BankReconciliationItem::class);
    }

    public function statementItems()
    {
        return $this->items()->where('type', 'statement');
    }

    public function bookItems()
    {
        return $this->items()->where('type', 'book');
    }

    public function matchedItems()
    {
        return $this->items()->where('status', 'matched');
    }

    public function unmatchedItems()
    {
        return $this->items()->where('status', 'unmatched');
    }

    // Scopes
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByAccount($query, $accountId)
    {
        return $query->where('account_id', $accountId);
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeDraft($query)
    {
        return $query->where('status', 'draft');
    }

    // Helper methods
    public function getStatusLabelAttribute()
    {
        return match($this->status) {
            'draft' => 'Draft',
            'in_progress' => 'Dalam Proses',
            'completed' => 'Selesai',
            'cancelled' => 'Dibatalkan',
            default => $this->status
        };
    }

    public function getStatusColorAttribute()
    {
        return match($this->status) {
            'draft' => 'gray',
            'in_progress' => 'warning',
            'completed' => 'success',
            'cancelled' => 'danger',
            default => 'gray'
        };
    }

    public function getTotalStatementAmountAttribute()
    {
        return $this->statementItems()->sum('amount');
    }

    public function getTotalBookAmountAttribute()
    {
        return $this->bookItems()->sum('amount');
    }

    public function getDifferenceAttribute()
    {
        return $this->closing_balance - $this->opening_balance - $this->getTotalBookAmountAttribute();
    }

    public function getMatchedCountAttribute()
    {
        return $this->matchedItems()->count();
    }

    public function getUnmatchedCountAttribute()
    {
        return $this->unmatchedItems()->count();
    }

    public function isCompleted()
    {
        return $this->status === 'completed';
    }

    public function canBeCompleted()
    {
        return $this->status === 'in_progress' && $this->getUnmatchedCountAttribute() === 0;
    }

    // Auto-generate reconciliation number
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($reconciliation) {
            if (empty($reconciliation->reconciliation_number)) {
                $reconciliation->reconciliation_number = static::generateReconciliationNumber();
            }
        });
    }

    public static function generateReconciliationNumber()
    {
        $prefix = 'REC';
        $date = now()->format('Ymd');
        $lastNumber = static::whereDate('created_at', now())
            ->where('reconciliation_number', 'like', $prefix . $date . '%')
            ->count();
        
        return $prefix . $date . str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
    }
}
