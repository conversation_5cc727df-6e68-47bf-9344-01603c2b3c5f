<div class="space-y-6">
    <!-- Header Info -->
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900">Detail Payroll</h3>
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                {{ $record->status === 'approved' ? 'bg-green-100 text-green-800' :
                   ($record->status === 'paid' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800') }}">
                {{ ucfirst($record->status) }}
            </span>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <dt class="text-sm font-medium text-gray-500">No. Payroll</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ $record->no_payroll }}</dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-500">Periode</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ $record->payrollPeriod->nama_periode }}</dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-500">Tanggal Approve</dt>
                <dd class="mt-1 text-sm text-gray-900">
                    {{ $record->approved_at ? $record->approved_at->format('d M Y H:i') : 'Belum diapprove' }}
                </dd>
            </div>
        </div>
    </div>

    <!-- Komponen Gaji -->
    <div class="bg-white rounded-lg shadow p-6">
        <h4 class="text-md font-medium text-gray-900 mb-4">Komponen Gaji</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <dt class="text-sm font-medium text-gray-500">Gaji Pokok</dt>
                <dd class="mt-1 text-sm text-gray-900">Rp {{ number_format($record->gaji_pokok, 0, ',', '.') }}</dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-500">Tunjangan Jabatan</dt>
                <dd class="mt-1 text-sm text-gray-900">Rp {{ number_format($record->tunjangan_jabatan, 0, ',', '.') }}</dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-500">Tunjangan Umum</dt>
                <dd class="mt-1 text-sm text-gray-900">Rp {{ number_format($record->tunjangan_umum, 0, ',', '.') }}</dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-500">Tunjangan Sembako</dt>
                <dd class="mt-1 text-sm text-gray-900">Rp {{ number_format($record->tunjangan_sembako, 0, ',', '.') }}</dd>
            </div>
        </div>

        <div class="mt-4 pt-4 border-t border-gray-200">
            <div class="flex justify-between">
                <dt class="text-sm font-medium text-gray-900">Total Gaji Kotor</dt>
                <dd class="text-sm font-bold text-gray-900">
                    Rp {{ number_format($record->gaji_pokok + $record->tunjangan_jabatan + $record->tunjangan_umum + $record->tunjangan_sembako, 0, ',', '.') }}
                </dd>
            </div>
        </div>
    </div>

    <!-- Potongan -->
    <div class="bg-white rounded-lg shadow p-6">
        <h4 class="text-md font-medium text-gray-900 mb-4">Potongan</h4>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <dt class="text-sm font-medium text-gray-500">BPJS Kesehatan</dt>
                <dd class="mt-1 text-sm text-red-600">- Rp {{ number_format($record->potongan_bpjs_kesehatan, 0, ',', '.') }}</dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-500">BPJS Ketenagakerjaan</dt>
                <dd class="mt-1 text-sm text-red-600">- Rp {{ number_format($record->potongan_bpjs_tk, 0, ',', '.') }}</dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-500">Potongan Keterlambatan</dt>
                <dd class="mt-1 text-sm text-red-600">- Rp {{ number_format($record->potongan_keterlambatan, 0, ',', '.') }}</dd>
            </div>
            <div>
                <dt class="text-sm font-medium text-gray-500">Potongan Pelanggaran</dt>
                <dd class="mt-1 text-sm text-red-600">- Rp {{ number_format($record->potongan_pelanggaran, 0, ',', '.') }}</dd>
            </div>
            @if($record->potongan_lainnya > 0)
            <div>
                <dt class="text-sm font-medium text-gray-500">Potongan Lainnya</dt>
                <dd class="mt-1 text-sm text-red-600">- Rp {{ number_format($record->potongan_lainnya, 0, ',', '.') }}</dd>
            </div>
            @endif
        </div>

        <div class="mt-4 pt-4 border-t border-gray-200">
            <div class="flex justify-between">
                <dt class="text-sm font-medium text-gray-900">Total Potongan</dt>
                <dd class="text-sm font-bold text-red-600">
                    - Rp {{ number_format($record->potongan_bpjs_kesehatan + $record->potongan_bpjs_tk + $record->potongan_keterlambatan + $record->potongan_pelanggaran + $record->potongan_lainnya, 0, ',', '.') }}
                </dd>
            </div>
        </div>
    </div>

    <!-- Take Home Pay -->
    <div class="bg-green-50 rounded-lg shadow p-6">
        <div class="flex justify-between items-center">
            <h4 class="text-lg font-bold text-green-900">Take Home Pay</h4>
            <span class="text-2xl font-bold text-green-600">
                Rp {{ number_format($record->take_home_pay, 0, ',', '.') }}
            </span>
        </div>
    </div>

    <!-- Detail Potongan -->
    @if($record->payrollDeductions->count() > 0)
    <div class="bg-white rounded-lg shadow p-6">
        <h4 class="text-md font-medium text-gray-900 mb-4">Detail Potongan</h4>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Jenis</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Keterangan</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Jumlah</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach($record->payrollDeductions as $deduction)
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {{ ucwords(str_replace('_', ' ', $deduction->jenis_potongan)) }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {{ $deduction->keterangan }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600">
                            - Rp {{ number_format($deduction->nominal, 0, ',', '.') }}
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
    @endif
</div>
