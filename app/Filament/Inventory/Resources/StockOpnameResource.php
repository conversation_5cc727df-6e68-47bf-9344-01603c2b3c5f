<?php

namespace App\Filament\Inventory\Resources;

use App\Filament\Inventory\Resources\StockOpnameResource\Pages;
use App\Filament\Inventory\Resources\StockOpnameResource\RelationManagers;
use App\Models\StockOpname;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class StockOpnameResource extends Resource
{
  protected static ?string $model = StockOpname::class;

  protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-check';

  protected static ?string $navigationLabel = 'Stock Opname';

  protected static ?string $modelLabel = 'Stock Opname';

  protected static ?string $pluralModelLabel = 'Stock Opname';

  protected static ?string $navigationGroup = 'Inventory Operations';

  protected static ?int $navigationSort = 3;

  public static function form(Form $form): Form
  {
    return $form
      ->schema([
        Forms\Components\Section::make('Informasi Stock Opname')
          ->schema([
            Forms\Components\DatePicker::make('opname_date')
              ->label('Tanggal Opname')
              ->required()
              ->default(now()),
            Forms\Components\Select::make('entitas_id')
              ->label('Entitas')
              ->relationship('entitas', 'nama')
              ->required()
              ->reactive()
              ->afterStateUpdated(function ($state, callable $set) {
                $set('warehouse_id', null);
              }),
            Forms\Components\Select::make('warehouse_id')
              ->label('Gudang')
              ->relationship('warehouse', 'name')
              ->required()
              ->reactive(),
            Forms\Components\Textarea::make('description')
              ->label('Deskripsi')
              ->required()
              ->columnSpanFull()
              ->default('Stock opname rutin'),
            Forms\Components\Textarea::make('notes')
              ->label('Catatan')
              ->columnSpanFull(),
          ])
          ->columns(2),

        Forms\Components\Section::make('Produk untuk Opname')
          ->schema([
            Forms\Components\Placeholder::make('info')
              ->label('')
              ->content('Setelah menyimpan stock opname, Anda dapat menambahkan produk yang akan dihitung pada tab "Items" di halaman edit.')
              ->visible(fn($livewire) => $livewire instanceof \Filament\Resources\Pages\CreateRecord),
          ])
          ->visible(fn($livewire) => $livewire instanceof \Filament\Resources\Pages\CreateRecord),
      ]);
  }

  public static function table(Table $table): Table
  {
    return $table
      ->columns([
        Tables\Columns\TextColumn::make('opname_number')
          ->label('Nomor Opname')
          ->searchable()
          ->sortable(),
        Tables\Columns\TextColumn::make('opname_date')
          ->label('Tanggal')
          ->date('d/m/Y')
          ->sortable(),
        Tables\Columns\TextColumn::make('entitas.nama')
          ->label('Entitas')
          ->searchable()
          ->sortable(),
        Tables\Columns\TextColumn::make('warehouse.name')
          ->label('Gudang')
          ->searchable()
          ->toggleable(),
        Tables\Columns\TextColumn::make('status')
          ->label('Status')
          ->badge()
          ->color(fn($record) => $record->status_color),
        Tables\Columns\TextColumn::make('total_items')
          ->label('Total Item')
          ->numeric(),
        Tables\Columns\TextColumn::make('counted_items')
          ->label('Sudah Dihitung')
          ->numeric(),
        Tables\Columns\TextColumn::make('counting_progress')
          ->label('Progress')
          ->formatStateUsing(fn($state) => $state . '%')
          ->color(fn($state) => $state == 100 ? 'success' : ($state > 50 ? 'warning' : 'danger')),
        Tables\Columns\TextColumn::make('total_variance_value')
          ->label('Total Varians')
          ->money('IDR')
          ->sortable(),
        Tables\Columns\TextColumn::make('created_at')
          ->label('Dibuat')
          ->dateTime('d/m/Y H:i')
          ->sortable()
          ->toggleable(),
      ])
      ->filters([
        Tables\Filters\SelectFilter::make('status')
          ->label('Status')
          ->options([
            'Draft' => 'Draft',
            'In_Progress' => 'Sedang Berlangsung',
            'Completed' => 'Selesai',
            'Cancelled' => 'Dibatalkan',
          ]),
        Tables\Filters\SelectFilter::make('entitas_id')
          ->label('Entitas')
          ->relationship('entitas', 'nama'),
        Tables\Filters\SelectFilter::make('warehouse_id')
          ->label('Gudang')
          ->relationship('warehouse', 'name'),
      ])
      ->actions([
        Tables\Actions\ViewAction::make(),
        Tables\Actions\EditAction::make()
          ->visible(fn($record) => $record->canBeEdited()),
        Tables\Actions\Action::make('start')
          ->label('Mulai')
          ->icon('heroicon-o-play')
          ->color('success')
          ->visible(fn($record) => $record->canBeStarted())
          ->requiresConfirmation()
          ->action(fn($record) => $record->startOpname()),
        Tables\Actions\Action::make('complete')
          ->label('Selesaikan')
          ->icon('heroicon-o-check')
          ->color('success')
          ->visible(fn($record) => $record->canBeCompleted())
          ->requiresConfirmation()
          ->action(fn($record) => $record->completeOpname()),
      ])
      ->bulkActions([
        Tables\Actions\BulkActionGroup::make([
          Tables\Actions\DeleteBulkAction::make(),
        ]),
      ])
      ->defaultSort('opname_date', 'desc');
  }

  public static function getRelations(): array
  {
    return [
      RelationManagers\StockOpnameItemsRelationManager::class,
    ];
  }

  public static function getPages(): array
  {
    return [
      'index' => Pages\ListStockOpnames::route('/'),
      'create' => Pages\CreateStockOpname::route('/create'),
      'edit' => Pages\EditStockOpname::route('/{record}/edit'),
    ];
  }
}
