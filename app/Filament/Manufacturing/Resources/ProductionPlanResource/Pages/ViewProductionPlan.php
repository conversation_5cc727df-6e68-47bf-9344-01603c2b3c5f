<?php

namespace App\Filament\Manufacturing\Resources\ProductionPlanResource\Pages;

use App\Filament\Manufacturing\Resources\ProductionPlanResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewProductionPlan extends ViewRecord
{
    protected static string $resource = ProductionPlanResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make(),
            
            Actions\Action::make('approve')
                ->label('Approve Plan')
                ->icon('heroicon-o-check-circle')
                ->color('success')
                ->requiresConfirmation()
                ->visible(fn () => $this->record->status === 'draft')
                ->action(function () {
                    $this->record->approve(auth()->user());
                    $this->refreshFormData(['status', 'approved_by', 'approved_at']);
                }),

            Actions\Action::make('start')
                ->label('Start Plan')
                ->icon('heroicon-o-play')
                ->color('warning')
                ->requiresConfirmation()
                ->visible(fn () => $this->record->status === 'approved')
                ->action(function () {
                    $this->record->start();
                    $this->refreshFormData(['status', 'started_at']);
                }),

            Actions\Action::make('complete')
                ->label('Complete Plan')
                ->icon('heroicon-o-check-circle')
                ->color('success')
                ->requiresConfirmation()
                ->visible(fn () => $this->record->status === 'in_progress')
                ->action(function () {
                    $this->record->complete();
                    $this->refreshFormData(['status', 'completed_at']);
                }),
        ];
    }
}
