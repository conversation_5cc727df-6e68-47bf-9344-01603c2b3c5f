<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProductionStageLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'production_stage_id',
        'action',
        'description',
        'quantity_processed',
        'quantity_good',
        'quantity_defective',
        'quantity_waste',
        'logged_by',
        'additional_data',
    ];

    protected $casts = [
        'quantity_processed' => 'decimal:2',
        'quantity_good' => 'decimal:2',
        'quantity_defective' => 'decimal:2',
        'quantity_waste' => 'decimal:2',
        'additional_data' => 'array',
    ];

    // Relationships
    public function productionStage(): BelongsTo
    {
        return $this->belongsTo(ProductionStage::class);
    }

    public function logger(): BelongsTo
    {
        return $this->belongsTo(User::class, 'logged_by');
    }

    // Scopes
    public function scopeForAction($query, string $action)
    {
        return $query->where('action', $action);
    }

    public function scopeRecent($query, int $days = 7)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    // Accessors
    public function getYieldPercentageAttribute(): float
    {
        if (!$this->quantity_processed || $this->quantity_processed == 0) {
            return 0;
        }
        
        return ($this->quantity_good / $this->quantity_processed) * 100;
    }

    public function getWastePercentageAttribute(): float
    {
        if (!$this->quantity_processed || $this->quantity_processed == 0) {
            return 0;
        }
        
        return ($this->quantity_waste / $this->quantity_processed) * 100;
    }

    public function getDefectivePercentageAttribute(): float
    {
        if (!$this->quantity_processed || $this->quantity_processed == 0) {
            return 0;
        }
        
        return ($this->quantity_defective / $this->quantity_processed) * 100;
    }
}
