<?php

namespace App\Filament\Manufacturing\Resources;

use App\Filament\Manufacturing\Resources\ShopFloorControlResource\Pages;
use App\Filament\Manufacturing\Resources\ShopFloorControlResource\RelationManagers;
use App\Models\ShopFloorControl;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ShopFloorControlResource extends Resource
{
    protected static ?string $model = ShopFloorControl::class;

    protected static ?string $navigationIcon = 'heroicon-o-cog-8-tooth';

    protected static ?string $navigationGroup = 'Operasi Produksi';

    protected static ?string $navigationLabel = 'Shop Floor Control';

    protected static ?string $modelLabel = 'Shop Floor Control';

    protected static ?string $pluralModelLabel = 'Shop Floor Control';

    protected static ?int $navigationSort = 4;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                //
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListShopFloorControls::route('/'),
            'create' => Pages\CreateShopFloorControl::route('/create'),
            'edit' => Pages\EditShopFloorControl::route('/{record}/edit'),
        ];
    }
}
