<?php

namespace App\Filament\Accounting\Resources\ExpenseCategoryResource\Pages;

use App\Filament\Accounting\Resources\ExpenseCategoryResource;
use Filament\Resources\Pages\CreateRecord;

class CreateExpenseCategory extends CreateRecord
{
    protected static string $resource = ExpenseCategoryResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getCreatedNotificationTitle(): ?string
    {
        return 'Kategori pengeluaran berhasil dibuat';
    }
}
