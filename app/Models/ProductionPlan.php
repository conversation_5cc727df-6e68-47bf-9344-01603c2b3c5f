<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProductionPlan extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'plan_number',
        'name',
        'description',
        'plan_start_date',
        'plan_end_date',
        'status',
        'priority',
        'total_planned_quantity',
        'total_actual_quantity',
        'total_planned_cost',
        'total_actual_cost',
        'total_planned_hours',
        'total_actual_hours',
        'warehouse_id',
        'created_by',
        'approved_by',
        'approved_at',
        'started_at',
        'completed_at',
        'notes',
    ];

    protected $casts = [
        'plan_start_date' => 'date',
        'plan_end_date' => 'date',
        'total_planned_quantity' => 'decimal:2',
        'total_actual_quantity' => 'decimal:2',
        'total_planned_cost' => 'decimal:2',
        'total_actual_cost' => 'decimal:2',
        'approved_at' => 'datetime',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    // Relationships
    public function warehouse(): BelongsTo
    {
        return $this->belongsTo(Warehouse::class);
    }

    public function productionOrders(): HasMany
    {
        return $this->hasMany(ProductionOrder::class);
    }

    public function productionSchedules(): HasMany
    {
        return $this->hasMany(ProductionSchedule::class);
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->whereIn('status', ['approved', 'in_progress']);
    }

    public function scopeInProgress($query)
    {
        return $query->where('status', 'in_progress');
    }

    public function scopeForWarehouse($query, $warehouseId)
    {
        return $query->where('warehouse_id', $warehouseId);
    }

    // Accessors
    public function getProgressPercentageAttribute(): float
    {
        if ($this->total_planned_quantity == 0) {
            return 0;
        }
        return ($this->total_actual_quantity / $this->total_planned_quantity) * 100;
    }

    public function getCostVarianceAttribute(): float
    {
        return $this->total_actual_cost - $this->total_planned_cost;
    }

    public function getScheduleVarianceAttribute(): int
    {
        if (!$this->completed_at || !$this->plan_end_date) {
            return 0;
        }
        return $this->completed_at->diffInDays($this->plan_end_date, false);
    }

    // Methods
    public function approve(User $user): void
    {
        $this->update([
            'status' => 'approved',
            'approved_by' => $user->id,
            'approved_at' => now(),
        ]);
    }

    public function start(): void
    {
        $this->update([
            'status' => 'in_progress',
            'started_at' => now(),
        ]);
    }

    public function complete(): void
    {
        $this->update([
            'status' => 'completed',
            'completed_at' => now(),
        ]);
    }

    public function updateTotals(): void
    {
        $orders = $this->productionOrders();
        
        $this->update([
            'total_planned_quantity' => $orders->sum('planned_quantity'),
            'total_actual_quantity' => $orders->sum('actual_quantity'),
            'total_planned_cost' => $orders->sum('total_planned_cost'),
            'total_actual_cost' => $orders->sum('total_actual_cost'),
            'total_planned_hours' => $orders->sum('planned_duration_hours'),
            'total_actual_hours' => $orders->sum('actual_duration_hours'),
        ]);
    }

    public function generatePlanNumber(): string
    {
        $prefix = 'PLAN';
        $date = now()->format('Ymd');
        $sequence = static::whereDate('created_at', today())->count() + 1;
        
        return sprintf('%s%s%04d', $prefix, $date, $sequence);
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($plan) {
            if (empty($plan->plan_number)) {
                $plan->plan_number = $plan->generatePlanNumber();
            }
        });
    }
}
