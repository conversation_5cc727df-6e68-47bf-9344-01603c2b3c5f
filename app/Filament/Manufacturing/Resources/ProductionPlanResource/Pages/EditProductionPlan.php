<?php

namespace App\Filament\Manufacturing\Resources\ProductionPlanResource\Pages;

use App\Filament\Manufacturing\Resources\ProductionPlanResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditProductionPlan extends EditRecord
{
    protected static string $resource = ProductionPlanResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('view', ['record' => $this->getRecord()]);
    }

    protected function afterSave(): void
    {
        // Update totals after save
        $this->record->updateTotals();
    }
}
