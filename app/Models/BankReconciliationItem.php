<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BankReconciliationItem extends Model
{
    use HasFactory;

    protected $table = 'bank_reconciliation_items';

    protected $fillable = [
        'bank_reconciliation_id',
        'journal_entry_id',
        'statement_reference',
        'transaction_date',
        'description',
        'amount',
        'type',
        'status',
        'matched_with',
        'notes',
    ];

    protected $casts = [
        'transaction_date' => 'date',
        'amount' => 'decimal:2',
    ];

    // Relationships
    public function bankReconciliation()
    {
        return $this->belongsTo(BankReconciliation::class);
    }

    public function journalEntry()
    {
        return $this->belongsTo(JournalEntry::class);
    }

    public function matchedItem()
    {
        return $this->belongsTo(BankReconciliationItem::class, 'matched_with');
    }

    public function matchedWith()
    {
        return $this->hasOne(BankReconciliationItem::class, 'matched_with');
    }

    // Scopes
    public function scopeStatementItems($query)
    {
        return $query->where('type', 'statement');
    }

    public function scopeBookItems($query)
    {
        return $query->where('type', 'book');
    }

    public function scopeMatched($query)
    {
        return $query->where('status', 'matched');
    }

    public function scopeUnmatched($query)
    {
        return $query->where('status', 'unmatched');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    // Helper methods
    public function getTypeLabelAttribute()
    {
        return match($this->type) {
            'statement' => 'Bank Statement',
            'book' => 'Buku Kas',
            default => $this->type
        };
    }

    public function getStatusLabelAttribute()
    {
        return match($this->status) {
            'unmatched' => 'Belum Cocok',
            'matched' => 'Sudah Cocok',
            'reconciled' => 'Sudah Rekonsiliasi',
            default => $this->status
        };
    }

    public function getStatusColorAttribute()
    {
        return match($this->status) {
            'unmatched' => 'warning',
            'matched' => 'info',
            'reconciled' => 'success',
            default => 'gray'
        };
    }

    public function getAmountFormattedAttribute()
    {
        return 'Rp ' . number_format($this->amount, 2, ',', '.');
    }

    public function isMatched()
    {
        return $this->status === 'matched';
    }

    public function isUnmatched()
    {
        return $this->status === 'unmatched';
    }

    public function canBeMatched()
    {
        return $this->status === 'unmatched';
    }

    public function matchWith(BankReconciliationItem $item)
    {
        if ($this->canBeMatched() && $item->canBeMatched()) {
            $this->update([
                'status' => 'matched',
                'matched_with' => $item->id,
            ]);

            $item->update([
                'status' => 'matched',
                'matched_with' => $this->id,
            ]);

            return true;
        }

        return false;
    }

    public function unmatch()
    {
        if ($this->isMatched() && $this->matched_with) {
            $matchedItem = $this->matchedItem;
            
            $this->update([
                'status' => 'unmatched',
                'matched_with' => null,
            ]);

            if ($matchedItem) {
                $matchedItem->update([
                    'status' => 'unmatched',
                    'matched_with' => null,
                ]);
            }

            return true;
        }

        return false;
    }
}
