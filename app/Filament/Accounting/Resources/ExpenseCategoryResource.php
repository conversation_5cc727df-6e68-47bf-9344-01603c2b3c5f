<?php

namespace App\Filament\Accounting\Resources;

use App\Filament\Accounting\Resources\ExpenseCategoryResource\Pages;
use App\Models\ExpenseCategory;
use App\Models\Akun;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class ExpenseCategoryResource extends Resource
{
    protected static ?string $model = ExpenseCategory::class;

    protected static ?string $navigationIcon = 'heroicon-o-currency-dollar';

    protected static ?string $navigationLabel = 'Kategori Pengeluaran';

    protected static ?string $modelLabel = 'Kategori Pengeluaran';

    protected static ?string $pluralModelLabel = 'Kategori Pengeluaran';

    protected static ?string $navigationGroup = 'Kategorisasi';

    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Kategori Pengeluaran')
                    ->schema([
                        Forms\Components\TextInput::make('code')
                            ->label('Kode Kategori')
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(20)
                            ->placeholder('Contoh: EXP001'),
                        
                        Forms\Components\TextInput::make('name')
                            ->label('Nama Kategori')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('Contoh: Biaya Operasional'),
                        
                        Forms\Components\Select::make('default_account_id')
                            ->label('Akun Default')
                            ->options(Akun::where('kategori_akun', 'Beban')->pluck('nama_akun', 'id'))
                            ->searchable()
                            ->preload()
                            ->helperText('Akun yang akan digunakan secara default untuk kategori ini'),
                        
                        Forms\Components\Textarea::make('description')
                            ->label('Deskripsi')
                            ->maxLength(500)
                            ->rows(3)
                            ->placeholder('Deskripsi kategori pengeluaran'),
                    ])
                    ->columns(2),
                
                Forms\Components\Section::make('Pengaturan Limit')
                    ->schema([
                        Forms\Components\TextInput::make('daily_limit')
                            ->label('Limit Harian')
                            ->numeric()
                            ->prefix('Rp')
                            ->step(0.01)
                            ->helperText('Batas maksimal pengeluaran per hari (kosongkan jika tidak ada limit)'),
                        
                        Forms\Components\TextInput::make('monthly_limit')
                            ->label('Limit Bulanan')
                            ->numeric()
                            ->prefix('Rp')
                            ->step(0.01)
                            ->helperText('Batas maksimal pengeluaran per bulan (kosongkan jika tidak ada limit)'),
                        
                        Forms\Components\Toggle::make('requires_receipt')
                            ->label('Wajib Bukti')
                            ->default(false)
                            ->helperText('Apakah kategori ini memerlukan bukti/receipt'),
                        
                        Forms\Components\Toggle::make('is_active')
                            ->label('Status Aktif')
                            ->default(true)
                            ->helperText('Kategori aktif akan muncul dalam pilihan pengeluaran'),
                    ])
                    ->columns(2),
                
                Forms\Components\Hidden::make('created_by')
                    ->default(auth()->id()),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('code')
                    ->label('Kode')
                    ->searchable()
                    ->sortable()
                    ->copyable(),
                
                Tables\Columns\TextColumn::make('name')
                    ->label('Nama Kategori')
                    ->searchable()
                    ->sortable()
                    ->wrap(),
                
                Tables\Columns\TextColumn::make('defaultAccount.nama_akun')
                    ->label('Akun Default')
                    ->searchable()
                    ->wrap()
                    ->placeholder('Tidak ada'),
                
                Tables\Columns\TextColumn::make('daily_limit')
                    ->label('Limit Harian')
                    ->money('IDR')
                    ->placeholder('Tidak ada limit'),
                
                Tables\Columns\TextColumn::make('monthly_limit')
                    ->label('Limit Bulanan')
                    ->money('IDR')
                    ->placeholder('Tidak ada limit'),
                
                Tables\Columns\IconColumn::make('requires_receipt')
                    ->label('Wajib Bukti')
                    ->boolean()
                    ->trueIcon('heroicon-o-document-text')
                    ->falseIcon('heroicon-o-x-mark')
                    ->trueColor('warning')
                    ->falseColor('gray'),
                
                Tables\Columns\IconColumn::make('is_active')
                    ->label('Status')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),
                
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('default_account_id')
                    ->label('Akun Default')
                    ->options(Akun::where('kategori_akun', 'Beban')->pluck('nama_akun', 'id'))
                    ->searchable(),
                
                Tables\Filters\TernaryFilter::make('requires_receipt')
                    ->label('Wajib Bukti')
                    ->boolean()
                    ->trueLabel('Wajib Bukti')
                    ->falseLabel('Tidak Wajib')
                    ->native(false),
                
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Status Aktif')
                    ->boolean()
                    ->trueLabel('Aktif')
                    ->falseLabel('Tidak Aktif')
                    ->native(false),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('activate')
                        ->label('Aktifkan')
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->action(function ($records) {
                            $records->each->update(['is_active' => true]);
                        })
                        ->requiresConfirmation(),
                    Tables\Actions\BulkAction::make('deactivate')
                        ->label('Nonaktifkan')
                        ->icon('heroicon-o-x-circle')
                        ->color('danger')
                        ->action(function ($records) {
                            $records->each->update(['is_active' => false]);
                        })
                        ->requiresConfirmation(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListExpenseCategories::route('/'),
            'create' => Pages\CreateExpenseCategory::route('/create'),
            'view' => Pages\ViewExpenseCategory::route('/{record}'),
            'edit' => Pages\EditExpenseCategory::route('/{record}/edit'),
        ];
    }
}
