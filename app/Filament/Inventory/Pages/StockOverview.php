<?php

namespace App\Filament\Inventory\Pages;

use App\Models\InventoryStock;
use App\Models\Product;
use App\Models\Category;
use App\Models\Warehouse;
use Filament\Forms;
use Filament\Pages\Page;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Illuminate\Database\Eloquent\Builder;

class StockOverview extends Page implements HasTable
{
    use InteractsWithTable;

    protected static ?string $navigationIcon = 'heroicon-o-archive-box';

    protected static string $view = 'filament.warehouse.pages.stock-overview';

    protected static ?string $title = 'Stock Overview';

    protected static ?string $navigationGroup = 'Reports & Analytics';

    protected static ?int $navigationSort = 3;

    public function table(Table $table): Table
    {
        return $table
            ->query(InventoryStock::query()->with(['product.category', 'warehouse', 'entitas']))
            ->columns([
                Tables\Columns\TextColumn::make('product.sku')
                    ->label('Product Code')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('product.name')
                    ->label('Product Name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('product.category.name')
                    ->label('Category')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('warehouse.name')
                    ->label('Warehouse')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('location_code')
                    ->label('Location')
                    ->searchable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('quantity')
                    ->label('Total Qty')
                    ->badge()
                    ->color(function ($state) {
                        return $state > 0 ? 'success' : 'danger';
                    }),
                Tables\Columns\TextColumn::make('available_quantity')
                    ->label('Available')
                    ->badge()
                    ->color('success'),
                Tables\Columns\TextColumn::make('on_hold_quantity')
                    ->label('On Hold')
                    ->badge()
                    ->color('warning'),
                Tables\Columns\TextColumn::make('reserved_quantity')
                    ->label('Reserved')
                    ->badge()
                    ->color('info'),
                Tables\Columns\TextColumn::make('minimum_stock')
                    ->label('Min Stock')
                    ->toggleable(),
                Tables\Columns\TextColumn::make('reorder_point')
                    ->label('Reorder Point')
                    ->toggleable(),
                Tables\Columns\TextColumn::make('available_stock_status')
                    ->label('Status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'Normal' => 'success',
                        'Low Available Stock' => 'warning',
                        'Reorder Required' => 'danger',
                        'Over Stock' => 'info',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('average_cost')
                    ->label('Avg Cost')
                    ->money('IDR')
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('total_value')
                    ->label('Total Value')
                    ->money('IDR')
                    ->sortable(),
                Tables\Columns\TextColumn::make('last_movement_at')
                    ->label('Last Movement')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('product.category_id')
                    ->label('Category')
                    ->options(Category::pluck('name', 'id'))
                    ->searchable()
                    ->preload(),
                Tables\Filters\SelectFilter::make('warehouse_id')
                    ->label('Warehouse')
                    ->options(Warehouse::where('is_active', true)->pluck('name', 'id'))
                    ->searchable()
                    ->preload(),
                Tables\Filters\SelectFilter::make('stock_status')
                    ->label('Stock Status')
                    ->options([
                        'normal' => 'Normal',
                        'low' => 'Low Stock',
                        'reorder' => 'Reorder Required',
                        'overstock' => 'Over Stock',
                        'out_of_stock' => 'Out of Stock',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when($data['value'], function (Builder $query, string $status) {
                            switch ($status) {
                                case 'low':
                                    return $query->whereColumn('available_quantity', '<=', 'minimum_stock')
                                        ->where('minimum_stock', '>', 0);
                                case 'reorder':
                                    return $query->whereColumn('available_quantity', '<=', 'reorder_point')
                                        ->where('reorder_point', '>', 0);
                                case 'overstock':
                                    return $query->whereColumn('quantity', '>=', 'maximum_stock')
                                        ->where('maximum_stock', '>', 0);
                                case 'out_of_stock':
                                    return $query->where('quantity', '<=', 0);
                                case 'normal':
                                    return $query->where('quantity', '>', 0)
                                        ->where(function ($q) {
                                            $q->whereColumn('available_quantity', '>', 'minimum_stock')
                                                ->orWhere('minimum_stock', '=', 0);
                                        });
                            }
                        });
                    }),
                Tables\Filters\TernaryFilter::make('is_batch_tracked')
                    ->label('Batch Tracked')
                    ->placeholder('All Items')
                    ->trueLabel('Batch Tracked')
                    ->falseLabel('Not Batch Tracked'),
            ])
            ->actions([
                Tables\Actions\Action::make('adjust_stock')
                    ->label('Adjust')
                    ->icon('heroicon-o-adjustments-horizontal')
                    ->color('warning')
                    ->form([
                        Forms\Components\Select::make('adjustment_type')
                            ->label('Adjustment Type')
                            ->options([
                                'increase' => 'Increase Stock',
                                'decrease' => 'Decrease Stock',
                            ])
                            ->required(),
                        Forms\Components\TextInput::make('quantity')
                            ->label('Quantity')
                            ->numeric()
                            ->required()
                            ->minValue(1),
                        Forms\Components\TextInput::make('reason')
                            ->label('Reason')
                            ->required()
                            ->placeholder('e.g., Damaged goods, Found items, etc.'),
                        Forms\Components\Textarea::make('notes')
                            ->label('Notes')
                            ->maxLength(500),
                    ])
                    ->action(function (InventoryStock $record, array $data) {
                        $adjustmentQuantity = $data['adjustment_type'] === 'increase'
                            ? $data['quantity']
                            : -$data['quantity'];

                        // Create stock movement record
                        \App\Models\StockMovement::create([
                            'movement_date' => now(),
                            'movement_type' => $data['adjustment_type'] === 'increase'
                                ? 'Adjustment_In'
                                : 'Adjustment_Out',
                            'product_id' => $record->product_id,
                            'warehouse_id' => $record->warehouse_id,
                            'entitas_id' => $record->entitas_id,
                            'quantity' => $adjustmentQuantity,
                            'unit_cost' => $record->average_cost,
                            'total_value' => $adjustmentQuantity * $record->average_cost,
                            'reference_number' => 'ADJ-' . now()->format('YmdHis'),
                            'notes' => $data['reason'] . (isset($data['notes']) ? ' - ' . $data['notes'] : ''),
                            'created_by' => auth()->id(),
                        ]);

                        // Update inventory stock
                        $record->quantity = max(0, $record->quantity + $adjustmentQuantity);
                        $record->available_quantity = max(0, $record->available_quantity + $adjustmentQuantity);
                        $record->last_movement_at = now();
                        $record->last_movement_type = $data['adjustment_type'] === 'increase'
                            ? 'Adjustment_In'
                            : 'Adjustment_Out';
                        $record->updateTotalValue();

                        $this->notify('success', 'Stock adjusted successfully');
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkAction::make('export')
                    ->label('Export Selected')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->action(function ($records) {
                        // Export functionality can be implemented here
                        $this->notify('info', 'Export functionality will be implemented');
                    }),
            ])
            ->defaultSort('product.name')
            ->striped()
            ->paginated([10, 25, 50, 100]);
    }
}
