<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("ALTER TABLE payroll_deductions CHANGE COLUMN jenis_potongan jenis_potongan ENUM('bpjs_kesehatan','bpjs_tk','keterlambatan','pelanggaran','kasir','stok_opname','retur','kasbon','sakit_tanpa_surat','alpha','cuti_melebihi_kuota','lainnya', 'tidak_absen_keluar') NOT NULL");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement("ALTER TABLE payroll_deductions CHANGE COLUMN jenis_potongan jenis_potongan ENUM('bpjs_kesehatan','bpjs_tk','keterlambatan','pelanggaran','kasir','stok_opname','retur','kasbon','sakit_tanpa_surat','alpha','cuti_melebihi_kuota','lainnya') NOT NULL");
    }
};
