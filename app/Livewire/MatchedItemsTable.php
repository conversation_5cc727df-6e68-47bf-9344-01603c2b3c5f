<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\BankReconciliation;
use App\Models\BankReconciliationItem;

class MatchedItemsTable extends Component
{
    public BankReconciliation $reconciliation;

    public function mount(BankReconciliation $reconciliation)
    {
        $this->reconciliation = $reconciliation;
    }

    public function unmatchPair($itemId)
    {
        $item = BankReconciliationItem::find($itemId);
        if ($item && $item->isMatched()) {
            $item->unmatch();
            $this->dispatch('item-unmatched');
        }
    }

    public function render()
    {
        $matchedItems = $this->reconciliation->items()
            ->where('status', 'matched')
            ->with('matchedItem')
            ->get()
            ->groupBy('matched_with')
            ->filter(function ($group) {
                return $group->count() == 2; // Only show complete pairs
            });

        return view('livewire.matched-items-table', [
            'matchedPairs' => $matchedItems
        ]);
    }
}
