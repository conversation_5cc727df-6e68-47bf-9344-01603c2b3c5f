<?php

namespace App\Filament\Manufacturing\Resources\QualityControlPointResource\Pages;

use App\Filament\Manufacturing\Resources\QualityControlPointResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditQualityControlPoint extends EditRecord
{
    protected static string $resource = QualityControlPointResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
