<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('quality_tests', function (Blueprint $table) {
            $table->id();
            $table->foreignId('quality_inspection_id')->constrained('quality_inspections')->onDelete('cascade');
            $table->string('test_name');
            $table->text('test_description')->nullable();
            $table->enum('test_type', ['measurement', 'visual', 'functional', 'chemical', 'physical'])->default('measurement');
            $table->decimal('measured_value', 10, 4)->nullable();
            $table->decimal('target_value', 10, 4)->nullable();
            $table->decimal('min_acceptable', 10, 4)->nullable();
            $table->decimal('max_acceptable', 10, 4)->nullable();
            $table->string('unit_of_measure', 20)->nullable();
            $table->enum('result', ['pass', 'fail', 'warning'])->default('pass');
            $table->text('observations')->nullable();
            $table->json('test_data')->nullable(); // For complex test data
            $table->string('equipment_used')->nullable();
            $table->foreignId('tested_by')->constrained('users');
            $table->datetime('test_date');
            $table->timestamps();

            // Indexes
            $table->index(['quality_inspection_id', 'test_date']);
            $table->index(['test_type', 'result']);
            $table->index(['tested_by', 'test_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('quality_tests');
    }
};
