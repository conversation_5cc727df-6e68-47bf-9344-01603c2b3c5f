<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class BomItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'bom_id',
        'component_product_id',
        'quantity_required',
        'unit',
        'unit_cost',
        'total_cost',
        'waste_percentage',
        'actual_quantity_required',
        'is_critical',
        'notes',
        'sequence',
    ];

    protected $casts = [
        'quantity_required' => 'decimal:4',
        'unit_cost' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'waste_percentage' => 'decimal:2',
        'actual_quantity_required' => 'decimal:4',
        'is_critical' => 'boolean',
    ];

    // Relationships
    public function bom(): BelongsTo
    {
        return $this->belongsTo(Bom::class);
    }

    public function componentProduct(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'component_product_id');
    }

    // Scopes
    public function scopeCritical($query)
    {
        return $query->where('is_critical', true);
    }

    public function scopeOrderedBySequence($query)
    {
        return $query->orderBy('sequence');
    }

    // Methods
    public function calculateActualQuantity(): void
    {
        $wasteMultiplier = 1 + ($this->waste_percentage / 100);
        $this->update([
            'actual_quantity_required' => $this->quantity_required * $wasteMultiplier,
        ]);
    }

    public function calculateTotalCost(): void
    {
        $this->update([
            'total_cost' => $this->quantity_required * $this->unit_cost,
        ]);
    }

    public function updateCostFromProduct(): void
    {
        if ($this->componentProduct) {
            // Get latest cost from inventory or product
            $latestCost = $this->componentProduct->cost ?? 0;
            $this->update([
                'unit_cost' => $latestCost,
                'total_cost' => $this->quantity_required * $latestCost,
            ]);
        }
    }

    protected static function boot()
    {
        parent::boot();

        static::saving(function ($bomItem) {
            // Auto-calculate total cost
            $bomItem->total_cost = $bomItem->quantity_required * $bomItem->unit_cost;
            
            // Auto-calculate actual quantity if waste percentage is set
            if ($bomItem->waste_percentage > 0) {
                $wasteMultiplier = 1 + ($bomItem->waste_percentage / 100);
                $bomItem->actual_quantity_required = $bomItem->quantity_required * $wasteMultiplier;
            } else {
                $bomItem->actual_quantity_required = $bomItem->quantity_required;
            }
        });

        static::saved(function ($bomItem) {
            // Update BOM total cost when item is saved
            $bomItem->bom->calculateTotalCost();
        });

        static::deleted(function ($bomItem) {
            // Update BOM total cost when item is deleted
            $bomItem->bom->calculateTotalCost();
        });
    }
}
