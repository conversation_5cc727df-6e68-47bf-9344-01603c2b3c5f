<?php

namespace App\Filament\Resources\KaryawanPermissionTypeResource\Pages;

use App\Filament\Resources\KaryawanPermissionTypeResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListKaryawanPermissionTypes extends ListRecords
{
    protected static string $resource = KaryawanPermissionTypeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
