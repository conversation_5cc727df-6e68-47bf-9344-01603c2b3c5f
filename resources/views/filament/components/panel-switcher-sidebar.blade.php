@php
    $user = auth()->user();
    $panels = collect();

    // Detect current panel from URL
    $currentUrl = request()->url();
    $currentPanelId = 'admin'; // default

    if (str_contains($currentUrl, '/admin')) {
        $currentPanelId = 'admin';
    } elseif (str_contains($currentUrl, '/karyawan')) {
        $currentPanelId = 'karyawan';
    } elseif (str_contains($currentUrl, '/pos')) {
        $currentPanelId = 'pos';
    } elseif (str_contains($currentUrl, '/marketing')) {
        $currentPanelId = 'marketing';
    } elseif (str_contains($currentUrl, '/akunting')) {
        $currentPanelId = 'akunting';
    } elseif (str_contains($currentUrl, '/accounting')) {
        $currentPanelId = 'accounting';
    } elseif (str_contains($currentUrl, '/finance')) {
        $currentPanelId = 'finance';
    // } elseif (str_contains($currentUrl, '/warehouse')) {
    //     $currentPanelId = 'warehouse';
    } elseif (str_contains($currentUrl, '/inventory')) {
        $currentPanelId = 'inventory';
    } elseif (str_contains($currentUrl, '/mitra')) {
        $currentPanelId = 'mitra';
    } elseif (str_contains($currentUrl, '/setting')) {
        $currentPanelId = 'setting';
    }

    // Admin Panel
    $panels->push([
        'id' => 'admin',
        'name' => 'Admin Panel',
        'url' => route('filament.admin.pages.dashboard'),
        'icon' => 'heroicon-o-cog-6-tooth',
        'current' => $currentPanelId === 'admin',
    ]);

    // Karyawan Panel
    if ($user && $user->karyawan()->exists()) {
        $panels->push([
            'id' => 'karyawan',
            'name' => 'Karyawan Panel',
            'url' => route('filament.karyawan.pages.dashboard'),
            'icon' => 'heroicon-o-user-group',
            'current' => $currentPanelId === 'karyawan',
        ]);
    }

    // POS Panel
    if ($user && $user->hasAnyRole(['super_admin'])) {
        $panels->push([
            'id' => 'pos',
            'name' => 'POS Panel',
            'url' => route('filament.pos.pages.dashboard'),
            'icon' => 'heroicon-o-shopping-cart',
            'current' => $currentPanelId === 'pos',
        ]);
    }

    // Marketing Panel
    if ($user && $user->hasAnyRole(['super_admin'])) {
        $panels->push([
            'id' => 'marketing',
            'name' => 'Marketing Panel',
            'url' => route('filament.marketing.pages.dashboard'),
            'icon' => 'heroicon-o-megaphone',
            'current' => $currentPanelId === 'marketing',
        ]);
    }

    // finance Panel
    if ($user && $user->hasAnyRole(['super_admin'])) {
        $panels->push([
            'id' => 'finance',
            'name' => 'Finance Panel',
            'url' => route('filament.finance.pages.dashboard'),
            'icon' => 'heroicon-o-currency-dollar',
            'current' => $currentPanelId === 'finance',
        ]);
    }

    // accounting Panel
    if ($user && $user->hasAnyRole(['super_admin', 'manager', 'manager_accounting'])) {
        $panels->push([
            'id' => 'accounting',
            'name' => 'Accounting Panel',
            'url' => route('filament.accounting.pages.dashboard'),
            'icon' => 'heroicon-o-calculator',
            'current' => $currentPanelId === 'accounting',
        ]);
    }

    // akunting Panel
    if ($user && $user->hasAnyRole(['super_admin', 'manager', 'manager_accounting'])) {
        $panels->push([
            'id' => 'akunting',
            'name' => 'Akunting Panel',
            'url' => route('filament.akunting.pages.dashboard'),
            'icon' => 'heroicon-o-calculator',
            'current' => $currentPanelId === 'akunting',
        ]);
    }

    // Warehouse Panel
    if ($user && $user->hasAnyRole(['super_admin', 'manager', 'supervisor'])) {
        $panels->push([
            'id' => 'warehouse',
            'name' => 'Warehouse Panel',
            'url' => route('filament.warehouse.pages.dashboard'),
            'icon' => 'heroicon-o-building-storefront',
            'current' => $currentPanelId === 'warehouse',
        ]);
    }

    // inventory Panel
    if ($user && $user->hasAnyRole(['super_admin'])) {
        $panels->push([
            'id' => 'inventory',
            'name' => 'Inventory Panel',
            'url' => route('filament.inventory.pages.dashboard'),
            'icon' => 'heroicon-o-building-storefront',
            'current' => $currentPanelId === 'inventory',
        ]);
    }

    // mitra Panel
    if ($user && $user->hasAnyRole(['super_admin'])) {
        $panels->push([
            'id' => 'mitra',
            'name' => 'Mitra Panel',
            'url' => route('filament.mitra.pages.dashboard'),
            'icon' => 'heroicon-o-building-office-2',
            'current' => $currentPanelId === 'mitra',
        ]);
    }

    // setting Panel
    if ($user && $user->hasAnyRole(['super_admin'])) {
        $panels->push([
            'id' => 'setting',
            'name' => 'Setting Panel',
            'url' => route('filament.setting.pages.dashboard'),
            'icon' => 'heroicon-o-adjustments-horizontal',
            'current' => $currentPanelId === 'setting',
        ]);
    }

    $currentPanel = $panels->firstWhere('current', true);
    $shouldShow = $panels->count() > 1;
@endphp

@if ($shouldShow)
    <p>List Panel </p>
    <div class="fi-sidebar-nav-item" x-data="{ open: false }">
        <button type="button" @click="open = !open"
            class="fi-sidebar-nav-item-button flex w-full items-center gap-x-3 rounded-lg px-3 py-2 text-sm font-medium outline-none transition duration-75 hover:bg-gray-100 focus-visible:bg-gray-100 dark:hover:bg-white/5 dark:focus-visible:bg-white/5 fi-sidebar-nav-item-active bg-gray-100 text-primary-600 dark:bg-white/5 dark:text-primary-400"
            :class="{ 'bg-gray-100 dark:bg-white/5': open }">
            <x-filament::icon :icon="$currentPanel['icon']"
                class="fi-sidebar-nav-item-icon h-6 w-6 text-primary-500 dark:text-primary-400" />

            <span class="fi-sidebar-nav-item-label flex-1 truncate text-start">
                {{ $currentPanel['name'] }}
            </span>

            <x-filament::icon icon="heroicon-m-chevron-down"
                class="h-4 w-4 text-gray-400 transition-transform duration-200 dark:text-gray-500"
                x-bind:class="{ 'rotate-180': open }" />
        </button>

        <div x-show="open" x-collapse class="mt-2 space-y-1">
            @foreach ($panels as $panel)
                @if (!$panel['current'])
                    <a href="{{ $panel['url'] }}"
                        class="fi-sidebar-nav-item-button flex w-full items-center gap-x-3 rounded-lg px-3 py-2 text-sm font-medium outline-none transition duration-75 hover:bg-gray-100 focus-visible:bg-gray-100 dark:hover:bg-white/5 dark:focus-visible:bg-white/5 ml-6">
                        <x-filament::icon :icon="$panel['icon']"
                            class="fi-sidebar-nav-item-icon h-5 w-5 text-gray-400 dark:text-gray-500" />

                        <span class="fi-sidebar-nav-item-label flex-1 truncate text-start">
                            {{ $panel['name'] }}
                        </span>
                    </a>
                @endif
            @endforeach
        </div>
    </div>
@endif
