<?php

namespace Database\Seeders;

use App\Models\Category;
use App\Models\Mitra;
use App\Models\Product;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ProductVieraQuinosSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->call([
            CategoryProductVieraQuinosSeeder::class,
            MitraVieraQuinosSeeder::class,
        ]);

        include database_path('seeders/data/product_viera.php');

        $count = count($dataList);
        $bar = $this->command->getOutput()->createProgressBar($count);
        $bar->start();

        try {
            DB::transaction(function () use ($dataList, $bar) {
                foreach ($dataList as $data) {
                    if (!Product::where($data)->first()) {
                        $data['category_id'] = Category::where('name', $data['category_id'])->first()->id;
                        $data['mitra_id'] = Mitra::where('nama_mitra', $data['mitra_id'])->first()->id;
                        Product::create($data);
                        $bar->advance();
                    }
                }
            });

            $bar->finish();
            $this->command->newLine();
            $this->command->info("✅ Selesai insert {$count} data Product.");
        } catch (\Throwable $e) {
            $bar->clear();
            $this->command->error("❌ Gagal insert data: " . $e->getMessage());
        }
    }
}
