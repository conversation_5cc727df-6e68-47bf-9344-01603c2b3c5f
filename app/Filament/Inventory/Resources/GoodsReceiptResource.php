<?php

namespace App\Filament\Inventory\Resources;

use App\Filament\Inventory\Resources\GoodsReceiptResource\Pages;
use App\Filament\Inventory\Resources\GoodsReceiptResource\RelationManagers;
use App\Models\GoodsReceipt;
use App\Models\PurchaseOrder;
use App\Models\Warehouse;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class GoodsReceiptResource extends Resource
{
  protected static ?string $model = GoodsReceipt::class;

  protected static ?string $navigationIcon = 'heroicon-o-truck';

  protected static ?string $navigationLabel = 'Goods Receipts';

  protected static ?string $navigationGroup = 'Procurement';

  protected static ?int $navigationSort = 2;

  // has access superadmin
  // public static function canAccess(): bool
  // {
  //     return auth()->user()->hasRole(['super_admin']);
  // }


  public static function form(Form $form): Form
  {
    return $form
      ->schema([
        Forms\Components\Section::make('Informasi Goods Receipt')
          ->schema([
            Forms\Components\Grid::make(3)
              ->schema([
                Forms\Components\TextInput::make('receipt_number')
                  ->label('Nomor Receipt')
                  ->disabled()
                  ->dehydrated(false)
                  ->placeholder('Auto-generated'),
                Forms\Components\DatePicker::make('receipt_date')
                  ->label('Tanggal Penerimaan')
                  ->required()
                  ->default(now()),
                Forms\Components\Select::make('status')
                  ->label('Status')
                  ->options([
                    'Draft' => 'Draft',
                    'Completed' => 'Completed',
                    'Cancelled' => 'Cancelled',
                  ])
                  ->default('Draft')
                  ->required(),
              ]),
            Forms\Components\Grid::make(2)
              ->schema([
                Forms\Components\Select::make('purchase_order_id')
                  ->label('Purchase Order')
                  ->options(function () {
                    return PurchaseOrder::with('supplier')
                      ->where('status', 'Approved')
                      ->orWhere('status', 'Partially_Received')
                      ->get()
                      ->mapWithKeys(function ($po) {
                        return [$po->id => $po->po_number . ' - ' . $po->supplier->name];
                      });
                  })
                  ->required()
                  ->searchable()
                  ->preload()
                  ->reactive()
                  ->afterStateUpdated(function ($state, callable $set) {
                    if ($state) {
                      $po = PurchaseOrder::find($state);
                      if ($po) {
                        $set('warehouse_id', $po->warehouse_id);
                      }
                    }
                  }),
                Forms\Components\Select::make('warehouse_id')
                  ->label('Gudang')
                  ->options(Warehouse::active()->get()->pluck('display_name', 'id'))
                  ->required()
                  ->searchable()
                  ->preload(),
              ]),
            Forms\Components\Grid::make(2)
              ->schema([
                Forms\Components\TextInput::make('delivery_note_number')
                  ->label('Nomor Surat Jalan')
                  ->placeholder('Nomor dari supplier'),
                Forms\Components\Select::make('received_by')
                  ->label('Diterima Oleh')
                  ->relationship('receivedBy', 'name')
                  ->searchable()
                  ->preload(),
              ]),
            Forms\Components\Textarea::make('notes')
              ->label('Catatan')
              ->rows(3)
              ->columnSpanFull(),
          ]),
      ]);
  }

  public static function table(Table $table): Table
  {
    return $table
      ->columns([
        Tables\Columns\TextColumn::make('receipt_number')
          ->label('Nomor Receipt')
          ->searchable()
          ->sortable(),
        Tables\Columns\TextColumn::make('receipt_date')
          ->label('Tanggal')
          ->date('d/m/Y')
          ->sortable(),
        Tables\Columns\TextColumn::make('purchaseOrder.po_number')
          ->label('Nomor PO')
          ->searchable(),
        Tables\Columns\TextColumn::make('purchaseOrder.supplier.nama')
          ->label('Supplier')
          ->searchable(),
        Tables\Columns\TextColumn::make('warehouse.name')
          ->label('Gudang')
          ->searchable(),
        Tables\Columns\TextColumn::make('total_items')
          ->label('Items')
          ->alignCenter(),
        Tables\Columns\TextColumn::make('total_quantity')
          ->label('Qty')
          ->alignCenter(),
        Tables\Columns\TextColumn::make('total_value')
          ->label('Total Nilai')
          ->money('IDR'),
        Tables\Columns\TextColumn::make('status')
          ->label('Status')
          ->badge()
          ->color(fn(string $state): string => match ($state) {
            'Draft' => 'gray',
            'Completed' => 'success',
            'Cancelled' => 'danger',
            default => 'gray',
          }),
        Tables\Columns\TextColumn::make('receivedBy.name')
          ->label('Diterima Oleh')
          ->searchable(),
      ])
      ->filters([
        Tables\Filters\SelectFilter::make('status')
          ->options([
            'Draft' => 'Draft',
            'Completed' => 'Completed',
            'Cancelled' => 'Cancelled',
          ]),
        Tables\Filters\SelectFilter::make('warehouse')
          ->relationship('warehouse', 'name'),
        Tables\Filters\TrashedFilter::make(),
      ])
      ->actions([
        Tables\Actions\ViewAction::make(),
        Tables\Actions\EditAction::make()
          ->visible(fn(GoodsReceipt $record) => $record->isEditable()),
        Tables\Actions\Action::make('complete')
          ->label('Complete')
          ->icon('heroicon-o-check')
          ->color('success')
          ->visible(fn(GoodsReceipt $record) => $record->canBeCompleted())
          ->requiresConfirmation()
          ->action(function (GoodsReceipt $record) {
            $record->complete();
          }),
        Tables\Actions\Action::make('cancel')
          ->label('Cancel')
          ->icon('heroicon-o-x-mark')
          ->color('danger')
          ->visible(fn(GoodsReceipt $record) => $record->canBeCancelled())
          ->requiresConfirmation()
          ->action(function (GoodsReceipt $record) {
            $record->status = 'Cancelled';
            $record->save();
          }),
      ])
      ->bulkActions([
        Tables\Actions\BulkActionGroup::make([
          Tables\Actions\DeleteBulkAction::make(),
          Tables\Actions\ForceDeleteBulkAction::make(),
          Tables\Actions\RestoreBulkAction::make(),
        ]),
      ])
      ->defaultSort('receipt_date', 'desc');
  }

  public static function getRelations(): array
  {
    return [
      RelationManagers\GoodsReceiptItemsRelationManager::class,
    ];
  }

  public static function getPages(): array
  {
    return [
      'index' => Pages\ListGoodsReceipts::route('/'),
      'create' => Pages\CreateGoodsReceipt::route('/create'),
      'view' => Pages\ViewGoodsReceipt::route('/{record}'),
      'edit' => Pages\EditGoodsReceipt::route('/{record}/edit'),
    ];
  }

  public static function getEloquentQuery(): Builder
  {
    return parent::getEloquentQuery()
      ->withoutGlobalScopes([
        SoftDeletingScope::class,
      ]);
  }
}
