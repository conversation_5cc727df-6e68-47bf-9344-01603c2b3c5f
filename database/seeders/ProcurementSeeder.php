<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\PurchaseOrder;
use App\Models\PurchaseOrderItem;
use App\Models\GoodsReceipt;
use App\Models\GoodsReceiptItem;
use App\Models\Supplier;
use App\Models\Warehouse;
use App\Models\Entitas;
use App\Models\Product;
use Carbon\Carbon;

class ProcurementSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Get existing data
        $suppliers = Supplier::all();
        $warehouses = Warehouse::all();
        $entitas = Entitas::all();
        $products = Product::all();

        if ($suppliers->isEmpty() || $warehouses->isEmpty() || $entitas->isEmpty() || $products->isEmpty()) {
            $this->command->warn('Please run MasterDataSeeder first to create suppliers, warehouses, entitas, and products.');
            return;
        }

        // Create sample Purchase Orders
        $purchaseOrders = [];

        for ($i = 1; $i <= 10; $i++) {
            $supplier = Supplier::inRandomOrder()->first();
            $warehouse = Warehouse::inRandomOrder()->first();
            $entitasItem = Entitas::inRandomOrder()->first();

            $po = PurchaseOrder::create([
                'po_date' => Carbon::now()->subDays(rand(1, 30)),
                'expected_delivery_date' => Carbon::now()->addDays(rand(1, 14)),
                'supplier_id' => $supplier->id,
                'warehouse_id' => $warehouse->id,
                'entitas_id' => $entitasItem->id,
                'subtotal' => 0, // Will be calculated
                'tax_amount' => 0,
                'discount_amount' => 0,
                'total_amount' => 0, // Will be calculated
                'status' => $this->getRandomStatus(),
                'notes' => 'Sample purchase order ' . $i,
                'payment_terms' => $this->getRandomPaymentTerms(),
                'created_by' => 1,
                'approved_by' => rand(0, 1) ? 1 : null,
                'approved_at' => rand(0, 1) ? Carbon::now()->subDays(rand(1, 5)) : null,
            ]);

            $purchaseOrders[] = $po;

            // Create PO Items
            $productCount = rand(2, min(5, $products->count()));
            $selectedProducts = Product::inRandomOrder()->take($productCount)->get();
            foreach ($selectedProducts as $product) {
                $quantity = rand(5, 50);
                $unitPrice = $product->cost_price * (1 + (rand(5, 20) / 100)); // Add 5-20% markup

                PurchaseOrderItem::create([
                    'purchase_order_id' => $po->id,
                    'product_id' => $product->id,
                    'quantity_ordered' => $quantity,
                    'quantity_received' => 0, // Will be updated when goods received
                    'unit_price' => $unitPrice,
                    'total_price' => $quantity * $unitPrice,
                    'notes' => 'Sample item for PO ' . $po->po_number,
                ]);
            }

            // Recalculate PO totals
            $po->calculateTotals();
        }

        // Create sample Goods Receipts for approved POs
        $approvedPOs = collect($purchaseOrders)->where('status', 'Approved');

        foreach ($approvedPOs->take(5) as $po) {
            $gr = GoodsReceipt::create([
                'receipt_date' => Carbon::now()->subDays(rand(1, 10)),
                'purchase_order_id' => $po->id,
                'warehouse_id' => $po->warehouse_id,
                'delivery_note_number' => 'DN-' . str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT),
                'status' => rand(0, 1) ? 'Completed' : 'Draft',
                'notes' => 'Sample goods receipt for PO ' . $po->po_number,
                'received_by' => 1,
            ]);

            // Create GR Items (receive partial or full quantities)
            foreach ($po->purchaseOrderItems as $poItem) {
                $receivePercentage = rand(70, 100) / 100; // Receive 70-100% of ordered quantity
                $quantityReceived = (int) ($poItem->quantity_ordered * $receivePercentage);

                if ($quantityReceived > 0) {
                    $unitCost = $poItem->unit_price * (1 + (rand(-5, 5) / 100)); // Allow ±5% variance

                    GoodsReceiptItem::create([
                        'goods_receipt_id' => $gr->id,
                        'purchase_order_item_id' => $poItem->id,
                        'product_id' => $poItem->product_id,
                        'quantity_received' => $quantityReceived,
                        'unit_cost' => $unitCost,
                        'total_cost' => $quantityReceived * $unitCost,
                        'notes' => 'Received for PO item',
                    ]);

                    // Update PO item quantity received
                    $poItem->quantity_received += $quantityReceived;
                    $poItem->save();
                }
            }

            // Update PO status based on receipt
            $po->updateReceiptStatus();

            // If GR is completed, update inventory (will be handled by complete() method)
            // if ($gr->status === 'Completed') {
            //     $gr->complete();
            // }
        }

        $this->command->info('Created ' . count($purchaseOrders) . ' purchase orders with items and ' . $approvedPOs->count() . ' goods receipts.');
    }

    private function getRandomStatus()
    {
        $statuses = ['Draft', 'Submitted', 'Approved', 'Partially_Received', 'Completed'];
        $weights = [20, 15, 40, 15, 10]; // Percentage weights

        $random = rand(1, 100);
        $cumulative = 0;

        foreach ($weights as $index => $weight) {
            $cumulative += $weight;
            if ($random <= $cumulative) {
                return $statuses[$index];
            }
        }

        return 'Draft';
    }

    private function getRandomPaymentTerms()
    {
        $terms = ['Net 30', 'Net 15', 'COD', 'Net 45', '2/10 Net 30'];
        return $terms[array_rand($terms)];
    }
}
