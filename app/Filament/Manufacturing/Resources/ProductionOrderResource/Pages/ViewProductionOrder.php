<?php

namespace App\Filament\Manufacturing\Resources\ProductionOrderResource\Pages;

use App\Filament\Manufacturing\Resources\ProductionOrderResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewProductionOrder extends ViewRecord
{
    protected static string $resource = ProductionOrderResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make(),
            
            Actions\Action::make('release')
                ->label('Release Order')
                ->icon('heroicon-o-play')
                ->color('success')
                ->requiresConfirmation()
                ->visible(fn () => $this->record->status === 'planned')
                ->action(function () {
                    $this->record->release();
                    $this->refreshFormData(['status']);
                }),

            Actions\Action::make('start')
                ->label('Start Production')
                ->icon('heroicon-o-play-circle')
                ->color('warning')
                ->requiresConfirmation()
                ->visible(fn () => $this->record->status === 'released')
                ->action(function () {
                    $this->record->start();
                    $this->refreshFormData(['status', 'actual_start_date']);
                }),

            Actions\Action::make('complete')
                ->label('Complete Order')
                ->icon('heroicon-o-check-circle')
                ->color('success')
                ->requiresConfirmation()
                ->visible(fn () => $this->record->status === 'in_progress')
                ->action(function () {
                    $this->record->complete();
                    $this->refreshFormData(['status', 'actual_end_date']);
                }),

            Actions\Action::make('hold')
                ->label('Put On Hold')
                ->icon('heroicon-o-pause')
                ->color('secondary')
                ->requiresConfirmation()
                ->visible(fn () => in_array($this->record->status, ['released', 'in_progress']))
                ->action(function () {
                    $this->record->update(['status' => 'on_hold']);
                    $this->refreshFormData(['status']);
                }),
        ];
    }
}
