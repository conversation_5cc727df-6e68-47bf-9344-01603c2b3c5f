<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\BankReconciliation;
use App\Models\BankReconciliationItem;

class BankReconciliationItemsTable extends Component
{
    public BankReconciliation $reconciliation;
    public string $type; // 'statement' or 'book'

    public function mount(BankReconciliation $reconciliation, string $type)
    {
        $this->reconciliation = $reconciliation;
        $this->type = $type;
    }

    public function matchItem($itemId, $targetItemId)
    {
        $item = BankReconciliationItem::find($itemId);
        $targetItem = BankReconciliationItem::find($targetItemId);

        if ($item && $targetItem && $item->canBeMatched() && $targetItem->canBeMatched()) {
            $item->matchWith($targetItem);
            $this->dispatch('item-matched');
        }
    }

    public function unmatchItem($itemId)
    {
        $item = BankReconciliationItem::find($itemId);
        if ($item && $item->isMatched()) {
            $item->unmatch();
            $this->dispatch('item-unmatched');
        }
    }

    public function render()
    {
        $items = $this->reconciliation->items()
            ->where('type', $this->type)
            ->orderBy('transaction_date', 'desc')
            ->get();

        return view('livewire.bank-reconciliation-items-table', [
            'items' => $items
        ]);
    }
}
