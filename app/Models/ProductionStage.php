<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ProductionStage extends Model
{
    use HasFactory;

    protected $fillable = [
        'production_order_id',
        'stage_name',
        'description',
        'sequence',
        'status',
        'planned_start_time',
        'planned_end_time',
        'actual_start_time',
        'actual_end_time',
        'planned_duration_minutes',
        'actual_duration_minutes',
        'planned_labor_cost',
        'actual_labor_cost',
        'assigned_to',
        'requires_quality_check',
        'quality_check_passed',
        'instructions',
        'notes',
    ];

    protected $casts = [
        'planned_start_time' => 'datetime',
        'planned_end_time' => 'datetime',
        'actual_start_time' => 'datetime',
        'actual_end_time' => 'datetime',
        'planned_labor_cost' => 'decimal:2',
        'actual_labor_cost' => 'decimal:2',
        'requires_quality_check' => 'boolean',
        'quality_check_passed' => 'boolean',
    ];

    // Relationships
    public function productionOrder(): BelongsTo
    {
        return $this->belongsTo(ProductionOrder::class);
    }

    public function assignedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function productionStageLogs(): HasMany
    {
        return $this->hasMany(ProductionStageLog::class);
    }

    public function qualityInspections(): HasMany
    {
        return $this->hasMany(QualityInspection::class);
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeInProgress($query)
    {
        return $query->where('status', 'in_progress');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    public function scopeOrderedBySequence($query)
    {
        return $query->orderBy('sequence');
    }

    // Accessors
    public function getProgressPercentageAttribute(): float
    {
        if ($this->status === 'completed') {
            return 100;
        }
        
        if ($this->status === 'pending') {
            return 0;
        }

        if ($this->actual_start_time && $this->planned_duration_minutes > 0) {
            $elapsedMinutes = now()->diffInMinutes($this->actual_start_time);
            return min(($elapsedMinutes / $this->planned_duration_minutes) * 100, 99);
        }

        return 0;
    }

    public function getDurationVarianceAttribute(): int
    {
        if (!$this->actual_start_time || !$this->actual_end_time) {
            return 0;
        }
        
        return $this->actual_duration_minutes - $this->planned_duration_minutes;
    }

    public function getIsDelayedAttribute(): bool
    {
        if ($this->status === 'completed') {
            return $this->actual_end_time > $this->planned_end_time;
        }
        
        if ($this->status === 'in_progress') {
            return now() > $this->planned_end_time;
        }
        
        return false;
    }

    // Methods
    public function start(User $user): void
    {
        $this->update([
            'status' => 'in_progress',
            'actual_start_time' => now(),
            'assigned_to' => $user->id,
        ]);

        $this->logAction('started', 'Stage started', $user);
    }

    public function complete(User $user, array $data = []): void
    {
        $actualEndTime = now();
        $actualDuration = $this->actual_start_time 
            ? $this->actual_start_time->diffInMinutes($actualEndTime)
            : $this->planned_duration_minutes;

        $this->update([
            'status' => 'completed',
            'actual_end_time' => $actualEndTime,
            'actual_duration_minutes' => $actualDuration,
            'quality_check_passed' => $data['quality_check_passed'] ?? null,
            'notes' => $data['notes'] ?? $this->notes,
        ]);

        $this->logAction('completed', 'Stage completed', $user, $data);
    }

    public function fail(User $user, string $reason): void
    {
        $this->update([
            'status' => 'failed',
            'actual_end_time' => now(),
            'notes' => $reason,
        ]);

        $this->logAction('failed', $reason, $user);
    }

    public function pause(User $user, string $reason = null): void
    {
        $this->logAction('paused', $reason ?? 'Stage paused', $user);
    }

    public function resume(User $user): void
    {
        $this->logAction('resumed', 'Stage resumed', $user);
    }

    public function logAction(string $action, string $description, User $user, array $additionalData = []): void
    {
        $this->productionStageLogs()->create([
            'action' => $action,
            'description' => $description,
            'logged_by' => $user->id,
            'additional_data' => $additionalData,
        ]);
    }

    public function canStart(): bool
    {
        if ($this->sequence <= 1) {
            return true;
        }

        // Check if previous stage is completed
        $previousStage = $this->productionOrder
            ->productionStages()
            ->where('sequence', $this->sequence - 1)
            ->first();

        return $previousStage && $previousStage->status === 'completed';
    }

    public function requiresQualityCheck(): bool
    {
        return $this->requires_quality_check;
    }
}
