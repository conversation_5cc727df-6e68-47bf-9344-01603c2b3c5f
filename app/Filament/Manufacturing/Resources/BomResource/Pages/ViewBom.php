<?php

namespace App\Filament\Manufacturing\Resources\BomResource\Pages;

use App\Filament\Manufacturing\Resources\BomResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewBom extends ViewRecord
{
    protected static string $resource = BomResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make(),
            Actions\Action::make('approve')
                ->label('Approve BOM')
                ->icon('heroicon-o-check-circle')
                ->color('success')
                ->requiresConfirmation()
                ->visible(fn () => $this->record->status === 'draft')
                ->action(function () {
                    $this->record->approve(auth()->user());
                    $this->refreshFormData(['status', 'approved_by', 'approved_at']);
                }),
        ];
    }
}
