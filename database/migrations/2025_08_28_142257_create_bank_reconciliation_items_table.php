<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bank_reconciliation_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('bank_reconciliation_id');
            $table->unsignedBigInteger('journal_entry_id')->nullable();
            $table->string('statement_reference')->nullable();
            $table->date('transaction_date');
            $table->text('description');
            $table->decimal('amount', 15, 2);
            $table->enum('type', ['statement', 'book']); // statement = dari bank statement, book = dari buku kas
            $table->enum('status', ['unmatched', 'matched', 'reconciled'])->default('unmatched');
            $table->unsignedBigInteger('matched_with')->nullable(); // ID item yang di-match
            $table->text('notes')->nullable();
            $table->timestamps();

            // Foreign keys
            $table->foreign('bank_reconciliation_id')->references('id')->on('bank_reconciliations')->onDelete('cascade');
            $table->foreign('journal_entry_id')->references('id')->on('journal_entries')->onDelete('set null');
            $table->foreign('matched_with')->references('id')->on('bank_reconciliation_items')->onDelete('set null');

            // Indexes
            $table->index(['bank_reconciliation_id', 'type']);
            $table->index('status');
            $table->index('transaction_date');
            $table->index('matched_with');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bank_reconciliation_items');
    }
};
