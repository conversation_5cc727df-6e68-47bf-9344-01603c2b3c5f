<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class TestFileUpload extends Command
{
    protected $signature = 'test:file-upload';
    protected $description = 'Test file upload functionality';

    public function handle()
    {
        $this->info('🧪 Testing File Upload Configuration...');

        // Test storage disk
        $this->info("\n📁 Storage Configuration:");
        $this->line("   Default disk: " . config('filesystems.default'));
        $this->line("   Public disk root: " . config('filesystems.disks.public.root'));
        $this->line("   Public disk URL: " . config('filesystems.disks.public.url'));

        // Test directory creation
        $this->info("\n📂 Testing Directory Creation:");
        try {
            $directory = 'expense-receipts';
            if (!Storage::disk('public')->exists($directory)) {
                Storage::disk('public')->makeDirectory($directory);
                $this->line("   ✅ Created directory: {$directory}");
            } else {
                $this->line("   ✅ Directory exists: {$directory}");
            }

            // Test file creation
            $testFile = $directory . '/test.txt';
            Storage::disk('public')->put($testFile, 'Test file content');
            $this->line("   ✅ Created test file: {$testFile}");

            // Test file URL
            $url = Storage::disk('public')->url($testFile);
            $this->line("   ✅ File URL: {$url}");

            // Clean up
            Storage::disk('public')->delete($testFile);
            $this->line("   ✅ Cleaned up test file");

        } catch (\Exception $e) {
            $this->error("   ❌ Error: " . $e->getMessage());
        }

        // Test PHP upload limits
        $this->info("\n⚙️ PHP Upload Configuration:");
        $this->line("   upload_max_filesize: " . ini_get('upload_max_filesize'));
        $this->line("   post_max_size: " . ini_get('post_max_size'));
        $this->line("   max_file_uploads: " . ini_get('max_file_uploads'));
        $this->line("   memory_limit: " . ini_get('memory_limit'));

        // Test permissions
        $this->info("\n🔐 Directory Permissions:");
        $storagePath = storage_path('app/public');
        $this->line("   Storage path: {$storagePath}");
        $this->line("   Exists: " . (is_dir($storagePath) ? 'Yes' : 'No'));
        $this->line("   Writable: " . (is_writable($storagePath) ? 'Yes' : 'No'));

        $publicPath = public_path('storage');
        $this->line("   Public symlink: {$publicPath}");
        $this->line("   Symlink exists: " . (is_link($publicPath) ? 'Yes' : 'No'));

        $this->info("\n🎉 File Upload Test Completed!");
        return 0;
    }
}
