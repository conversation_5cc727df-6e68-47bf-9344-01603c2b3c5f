<?php

namespace App\Filament\Warehouse\Resources;

use App\Filament\Warehouse\Resources\StockMovementResource\Pages;
use App\Models\StockMovement;
use App\Models\Product;
use App\Models\Warehouse;
use App\Models\Entitas;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class StockMovementResource extends Resource
{
    protected static ?string $model = StockMovement::class;

    protected static ?string $navigationIcon = 'heroicon-o-arrows-right-left';

    protected static ?string $navigationGroup = 'Inventory Management';

    protected static ?int $navigationSort = 1;

    protected static ?string $navigationLabel = 'Stock Movement';

    protected static ?string $modelLabel = 'Stock Movement';

    protected static ?string $pluralModelLabel = 'Stock Movements';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Stock Movement')
                    ->schema([
                        Forms\Components\TextInput::make('movement_number')
                            ->label('Nomor Movement')
                            ->disabled()
                            ->dehydrated(false)
                            ->placeholder('Auto-generated'),
                        Forms\Components\DatePicker::make('movement_date')
                            ->label('Tanggal Movement')
                            ->required()
                            ->default(now()),
                        Forms\Components\Select::make('movement_type')
                            ->label('Tipe Movement')
                            ->options([
                                'Opening_Balance' => 'Opening Balance',
                                'Purchase_Receipt' => 'Purchase Receipt',
                                'Sales_Issue' => 'Sales Issue',
                                'Transfer_In' => 'Transfer In',
                                'Transfer_Out' => 'Transfer Out',
                                'Adjustment_In' => 'Adjustment In',
                                'Adjustment_Out' => 'Adjustment Out',
                                'Production_In' => 'Production In',
                                'Production_Out' => 'Production Out',
                            ])
                            ->required(),
                        Forms\Components\Select::make('product_id')
                            ->label('Produk')
                            ->options(Product::with('category')->get()->pluck('name', 'id'))
                            ->searchable()
                            ->preload()
                            ->required(),
                        Forms\Components\Select::make('warehouse_id')
                            ->label('Gudang')
                            ->options(Warehouse::where('is_active', true)->pluck('name', 'id'))
                            ->searchable()
                            ->preload()
                            ->required(),
                        Forms\Components\Select::make('entitas_id')
                            ->label('Entitas')
                            ->options(Entitas::where('is_active', true)->pluck('nama', 'id'))
                            ->searchable()
                            ->preload()
                            ->required(),
                        Forms\Components\TextInput::make('quantity')
                            ->label('Quantity')
                            ->numeric()
                            ->required()
                            ->helperText('Positive for incoming, negative for outgoing'),
                        Forms\Components\TextInput::make('unit_cost')
                            ->label('Unit Cost')
                            ->numeric()
                            ->prefix('Rp')
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(function ($state, Forms\Get $get, Forms\Set $set) {
                                $quantity = $get('quantity') ?? 0;
                                $set('total_value', $quantity * $state);
                            }),
                        Forms\Components\TextInput::make('total_value')
                            ->label('Total Value')
                            ->numeric()
                            ->prefix('Rp')
                            ->disabled()
                            ->dehydrated(),
                        Forms\Components\TextInput::make('reference_number')
                            ->label('Reference Number')
                            ->maxLength(255)
                            ->placeholder('External reference number'),
                        Forms\Components\Textarea::make('notes')
                            ->label('Notes')
                            ->maxLength(65535)
                            ->columnSpanFull(),
                    ])
                    ->columns(3),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('movement_number')
                    ->label('Movement #')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('movement_date')
                    ->label('Date')
                    ->date('d/m/Y')
                    ->sortable(),
                Tables\Columns\BadgeColumn::make('movement_type')
                    ->label('Type')
                    ->colors([
                        'success' => ['Purchase_Receipt', 'Transfer_In', 'Adjustment_In', 'Production_In', 'Opening_Balance'],
                        'danger' => ['Sales_Issue', 'Transfer_Out', 'Adjustment_Out', 'Production_Out'],
                    ]),
                Tables\Columns\TextColumn::make('product.nama')
                    ->label('Product')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('warehouse.name')
                    ->label('Warehouse')
                    ->searchable(),
                Tables\Columns\TextColumn::make('quantity')
                    ->label('Quantity')
                    ->badge()
                    ->color(function ($state) {
                        return $state > 0 ? 'success' : 'danger';
                    })
                    ->formatStateUsing(function ($state) {
                        return ($state > 0 ? '+' : '') . number_format($state);
                    }),
                Tables\Columns\TextColumn::make('unit_cost')
                    ->label('Unit Cost')
                    ->money('IDR'),
                Tables\Columns\TextColumn::make('total_value')
                    ->label('Total Value')
                    ->money('IDR')
                    ->color(function ($state) {
                        return $state > 0 ? 'success' : 'danger';
                    }),
                Tables\Columns\TextColumn::make('reference_number')
                    ->label('Reference')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('movement_type')
                    ->label('Movement Type')
                    ->options([
                        'Opening_Balance' => 'Opening Balance',
                        'Purchase_Receipt' => 'Purchase Receipt',
                        'Sales_Issue' => 'Sales Issue',
                        'Transfer_In' => 'Transfer In',
                        'Transfer_Out' => 'Transfer Out',
                        'Adjustment_In' => 'Adjustment In',
                        'Adjustment_Out' => 'Adjustment Out',
                        'Production_In' => 'Production In',
                        'Production_Out' => 'Production Out',
                    ]),
                Tables\Filters\SelectFilter::make('product_id')
                    ->label('Product')
                    ->options(Product::pluck('name', 'id'))
                    ->searchable()
                    ->preload(),
                Tables\Filters\SelectFilter::make('warehouse_id')
                    ->label('Warehouse')
                    ->options(Warehouse::pluck('name', 'id'))
                    ->searchable()
                    ->preload(),
                Tables\Filters\Filter::make('movement_date')
                    ->form([
                        Forms\Components\DatePicker::make('from')
                            ->label('From Date'),
                        Forms\Components\DatePicker::make('until')
                            ->label('Until Date'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('movement_date', '>=', $date),
                            )
                            ->when(
                                $data['until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('movement_date', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make()
                    ->visible(fn($record) => $record->movement_type === 'Adjustment_In' || $record->movement_type === 'Adjustment_Out'),
                Tables\Actions\DeleteAction::make()
                    ->visible(fn($record) => $record->movement_type === 'Adjustment_In' || $record->movement_type === 'Adjustment_Out'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('movement_date', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListStockMovements::route('/'),
            'create' => Pages\CreateStockMovement::route('/create'),
            'view' => Pages\ViewStockMovement::route('/{record}'),
            'edit' => Pages\EditStockMovement::route('/{record}/edit'),
        ];
    }
}
