<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payroll_transactions', function (Blueprint $table) {
            $table->integer('total_hari_tidak_absen_keluar')->default(0)->after('total_hari_hadir')->comment('Total hari karyawan tidak melakukan absen keluar meskipun sudah absen masuk');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payroll_transactions', function (Blueprint $table) {
            $table->dropColumn('total_hari_tidak_absen_keluar');
        });
    }
};
