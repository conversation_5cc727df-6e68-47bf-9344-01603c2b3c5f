<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("ALTER TABLE expense_request_approvals CHANGE COLUMN status status ENUM('Draft', 'Submitted', 'Approved', 'Rejected', 'Paid', 'Cancelled', 'Revision') NOT NULL DEFAULT 'Draft'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::statement("ALTER TABLE expense_request_approvals CHANGE COLUMN status status ENUM('Draft', 'Submitted', 'Approved', 'Rejected', 'Paid', 'Cancelled') NOT NULL DEFAULT 'Draft'");
    }
};
