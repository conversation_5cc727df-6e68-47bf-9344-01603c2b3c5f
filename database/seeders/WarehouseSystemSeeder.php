<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Warehouse;
use App\Models\Product;
use App\Models\Category;
use App\Models\Satuan;
use App\Models\InventoryStock;
use App\Models\StockMovement;
use App\Models\Entitas;
use Carbon\Carbon;

class WarehouseSystemSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample warehouses if they don't exist
        $warehouses = [
            [
                'name' => 'Gudang Utama Jakarta',
                'code' => 'GDG-JKT-001',
                'address' => 'Jl. Raya Jakarta No. 123, Jakarta Pusat',
                'phone' => '021-12345678',
                'manager_name' => 'Budi Santoso',
                'is_active' => true,
            ],
            [
                'name' => 'Gudang Cabang Surabaya',
                'code' => 'GDG-SBY-001',
                'address' => 'Jl. Raya Surabaya No. 456, Surabaya',
                'phone' => '031-87654321',
                'manager_name' => 'Siti <PERSON>hayu',
                'is_active' => true,
            ],
            [
                'name' => 'Gudang Transit Bandung',
                'code' => 'GDG-BDG-001',
                'address' => 'Jl. Raya Bandung No. 789, Bandung',
                'phone' => '022-11223344',
                'manager_name' => 'Ahmad Wijaya',
                'is_active' => true,
            ],
        ];

        foreach ($warehouses as $warehouseData) {
            Warehouse::firstOrCreate(
                ['code' => $warehouseData['code']],
                $warehouseData
            );
        }

        // Create sample product categories if they don't exist
        $categories = [
            ['nama' => 'Elektronik', 'deskripsi' => 'Produk elektronik dan gadget', 'created_by' => 1],
            ['nama' => 'Pakaian', 'deskripsi' => 'Pakaian dan aksesoris', 'created_by' => 1],
            ['nama' => 'Makanan & Minuman', 'deskripsi' => 'Produk makanan dan minuman', 'created_by' => 1],
            ['nama' => 'Peralatan Rumah Tangga', 'deskripsi' => 'Peralatan untuk rumah tangga', 'created_by' => 1],
        ];

        foreach ($categories as $categoryData) {
            Category::firstOrCreate(
                ['name' => $categoryData['nama']],
                [
                    'name' => $categoryData['nama'],
                    'description' => $categoryData['deskripsi'],
                    'created_by' => $categoryData['created_by']
                ]
            );
        }

        // Create sample units if they don't exist
        $units = [
            ['nama_satuan' => 'Pcs', 'deskripsi_satuan' => 'Pieces', 'created_by' => 1],
            ['nama_satuan' => 'Kg', 'deskripsi_satuan' => 'Kilogram', 'created_by' => 1],
            ['nama_satuan' => 'Liter', 'deskripsi_satuan' => 'Liter', 'created_by' => 1],
            ['nama_satuan' => 'Box', 'deskripsi_satuan' => 'Box', 'created_by' => 1],
            ['nama_satuan' => 'Set', 'deskripsi_satuan' => 'Set', 'created_by' => 1],
        ];

        foreach ($units as $unitData) {
            Satuan::firstOrCreate(
                ['nama_satuan' => $unitData['nama_satuan']],
                $unitData
            );
        }

        // Create sample products if they don't exist
        $elektronikCategory = Category::where('name', 'Elektronik')->first();
        $pakaianCategory = Category::where('name', 'Pakaian')->first();
        $makananCategory = Category::where('name', 'Makanan & Minuman')->first();



        $products = [
            [
                'sku' => 'ELK-001',
                'name' => 'Smartphone Samsung Galaxy A54',
                'description' => 'Smartphone Android dengan kamera 50MP',
                'category_id' => $elektronikCategory->id,
                'cost_price' => 3500000,
                'price' => 4200000,
                'is_active' => true,
                'is_sellable' => true,
                'is_purchasable' => true,
                'primary_unit' => 'pcs',
                'stock_quantity' => 0,
            ],
            [
                'sku' => 'ELK-002',
                'name' => 'Laptop ASUS VivoBook',
                'description' => 'Laptop dengan processor Intel Core i5',
                'category_id' => $elektronikCategory->id,
                'cost_price' => 8500000,
                'price' => 10200000,
                'is_active' => true,
                'is_sellable' => true,
                'is_purchasable' => true,
                'primary_unit' => 'pcs',
                'stock_quantity' => 0,
            ],
            [
                'sku' => 'PKN-001',
                'name' => 'Kaos Polo Pria',
                'description' => 'Kaos polo cotton combed 30s',
                'category_id' => $pakaianCategory->id,
                'cost_price' => 75000,
                'price' => 120000,
                'is_active' => true,
                'is_sellable' => true,
                'is_purchasable' => true,
                'primary_unit' => 'pcs',
                'stock_quantity' => 0,
            ],
            [
                'sku' => 'MKN-001',
                'name' => 'Kopi Arabica Premium',
                'description' => 'Kopi arabica premium grade A',
                'category_id' => $makananCategory->id,
                'cost_price' => 150000,
                'price' => 200000,
                'is_active' => true,
                'is_sellable' => true,
                'is_purchasable' => true,
                'is_food_item' => true,
                'primary_unit' => 'kg',
                'stock_quantity' => 0,
            ],
            [
                'sku' => 'MKN-002',
                'name' => 'Teh Hijau Organik',
                'description' => 'Teh hijau organik tanpa pestisida',
                'category_id' => $makananCategory->id,
                'cost_price' => 45000,
                'price' => 65000,
                'is_active' => true,
                'is_sellable' => true,
                'is_purchasable' => true,
                'is_food_item' => true,
                'primary_unit' => 'box',
                'stock_quantity' => 0,
            ],
        ];

        foreach ($products as $productData) {
            Product::firstOrCreate(
                ['sku' => $productData['sku']],
                $productData
            );
        }

        // Create sample entitas if it doesn't exist
        $entitas = Entitas::firstOrCreate([
            'nama' => 'PT Viera Digital',
        ], [
            'nama' => 'PT Viera Digital',
            'alamat' => 'Jakarta',
            'keterangan' => 'Entitas utama untuk testing warehouse system',
        ]);

        // Create sample inventory stocks
        $warehouses = Warehouse::all();
        $products = Product::all();

        foreach ($warehouses as $warehouse) {
            foreach ($products as $product) {
                $quantity = rand(10, 100);
                $availableQty = $quantity - rand(0, 10);
                $onHoldQty = rand(0, 5);
                $reservedQty = rand(0, 5);

                InventoryStock::firstOrCreate(
                    [
                        'product_id' => $product->id,
                        'warehouse_id' => $warehouse->id,
                        'entitas_id' => $entitas->id,
                    ],
                    [
                        'quantity' => $quantity,
                        'available_quantity' => $availableQty,
                        'on_hold_quantity' => $onHoldQty,
                        'reserved_quantity' => $reservedQty,
                        'average_cost' => $product->unit_cost,
                        'total_value' => $quantity * $product->unit_cost,
                        'minimum_stock' => rand(5, 15),
                        'maximum_stock' => rand(150, 300),
                        'reorder_point' => rand(10, 20),
                        'safety_stock' => rand(5, 10),
                        'location_code' => 'A' . rand(1, 5) . '-B' . rand(1, 10) . '-C' . rand(1, 20),
                        'is_batch_tracked' => rand(0, 1),
                        'last_updated' => now(),
                        'last_movement_at' => now()->subDays(rand(1, 30)),
                        'last_movement_type' => 'Opening_Balance',
                    ]
                );
            }
        }

        // Create sample stock movements
        $movementTypes = [
            'Opening_Balance',
            'Purchase_Receipt',
            'Sales_Issue',
            'Adjustment_In',
            'Adjustment_Out',
        ];

        for ($i = 0; $i < 50; $i++) {
            $product = $products->random();
            $warehouse = $warehouses->random();
            $movementType = $movementTypes[array_rand($movementTypes)];
            $quantity = rand(1, 20);

            if (in_array($movementType, ['Sales_Issue', 'Adjustment_Out'])) {
                $quantity = -$quantity;
            }

            StockMovement::create([
                'movement_number' => 'MOV-' . now()->format('Ymd') . '-' . str_pad($i + 1, 4, '0', STR_PAD_LEFT),
                'movement_date' => now()->subDays(rand(0, 30)),
                'movement_type' => $movementType,
                'product_id' => $product->id,
                'warehouse_id' => $warehouse->id,
                'entitas_id' => $entitas->id,
                'quantity' => $quantity,
                'unit_cost' => $product->unit_cost,
                'total_value' => $quantity * $product->unit_cost,
                'reference_number' => 'REF-' . rand(1000, 9999),
                'notes' => 'Sample movement for testing',
                'created_by' => 1, // Assuming user ID 1 exists
            ]);
        }

        $this->command->info('Warehouse system sample data created successfully!');
    }
}
