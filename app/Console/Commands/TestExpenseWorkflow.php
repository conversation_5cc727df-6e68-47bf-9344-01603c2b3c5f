<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ExpenseRequest;
use App\Models\ExpenseRequestItem;
use App\Models\ExpenseCategory;
use App\Models\Karyawan;
use App\Models\Entitas;
use App\Models\Akun;
use App\Models\User;
use App\Models\Journal;

class TestExpenseWorkflow extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:expense-workflow';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the complete expense request workflow';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧪 Testing Expense Request Workflow...');

        try {
            // Get required data
            $user = User::first();
            $employee = Karyawan::first();
            $entitas = Entitas::first();
            $expenseCategory = ExpenseCategory::first();
            $cashAccount = Akun::where('kategori_akun', 'Kas')->first();

            if (!$user || !$employee || !$entitas || !$expenseCategory || !$cashAccount) {
                $this->error('❌ Missing required data. Please ensure you have users, employees, entitas, expense categories, and cash accounts.');
                return 1;
            }

            $this->info("📋 Test Data:");
            $this->line("   User: {$user->name}");
            $this->line("   Employee: {$employee->nama_lengkap}");
            $this->line("   Entitas: {$entitas->nama}");
            $this->line("   Expense Category: {$expenseCategory->name}");
            $this->line("   Cash Account: {$cashAccount->nama_akun}");

            // 1. Create expense request
            $this->info("\n1️⃣ Creating expense request...");
            $expenseRequest = ExpenseRequest::create([
                'request_number' => 'ER-TEST-' . time(),
                'request_date' => now()->toDateString(),
                'employee_id' => $employee->id,
                'entitas_id' => $entitas->id,
                'expense_type' => 'Petty_Cash',
                'total_amount' => 0,
                'status' => 'Draft',
                'purpose' => 'Test expense request for workflow validation',
                'notes' => 'Automated test',
                'requested_by' => $user->id,
            ]);
            $this->line("   ✅ Created: {$expenseRequest->request_number}");

            // 2. Add expense item
            $this->info("\n2️⃣ Adding expense item...");
            $expenseItem = ExpenseRequestItem::create([
                'expense_request_id' => $expenseRequest->id,
                'expense_category_id' => $expenseCategory->id,
                'description' => 'Test expense item',
                'amount' => 150000,
                'expense_date' => now()->toDateString(),
                'account_id' => $expenseCategory->default_account_id,
            ]);

            $expenseRequest->refresh();
            $this->line("   ✅ Item added: Rp " . number_format($expenseItem->amount));
            $this->line("   ✅ Total updated: Rp " . number_format($expenseRequest->total_amount));

            // 3. Submit expense request
            $this->info("\n3️⃣ Submitting expense request...");
            $expenseRequest->submit();
            $this->line("   ✅ Status: {$expenseRequest->status}");

            // 4. Approve with payment method
            $this->info("\n4️⃣ Approving expense request...");
            $approvalData = [
                'payment_method' => 'Cash',
                'cash_account_id' => $cashAccount->id,
                'approval_notes' => 'Approved via automated test',
            ];

            // Set auth user for approval
            auth()->login($user);
            $expenseRequest->approve($approvalData);

            $this->line("   ✅ Status: {$expenseRequest->status}");
            $this->line("   ✅ Approved by: {$expenseRequest->approvedBy->name}");
            $this->line("   ✅ Payment method: {$approvalData['payment_method']}");

            // 5. Check journal entries
            $this->info("\n5️⃣ Checking journal entries...");
            $journal = Journal::where('source_type', 'ExpenseRequest')
                ->where('source_id', $expenseRequest->id)
                ->first();

            if ($journal) {
                $this->line("   ✅ Journal created: {$journal->journal_number}");
                $this->line("   ✅ Journal status: {$journal->status}");

                $journalEntries = $journal->journalEntries;
                $totalDebit = $journalEntries->sum('debit');
                $totalCredit = $journalEntries->sum('credit');

                $this->line("   ✅ Total entries: {$journalEntries->count()}");
                $this->line("   ✅ Total debit: Rp " . number_format($totalDebit));
                $this->line("   ✅ Total credit: Rp " . number_format($totalCredit));
                $this->line("   ✅ Balanced: " . ($totalDebit == $totalCredit ? 'Yes' : 'No'));
            } else {
                $this->error("   ❌ No journal entries found");
            }

            $this->info("\n🎉 Expense Request Workflow Test Completed Successfully!");
            return 0;
        } catch (\Exception $e) {
            $this->error("❌ Test failed: " . $e->getMessage());
            $this->error("Stack trace: " . $e->getTraceAsString());
            return 1;
        }
    }
}
