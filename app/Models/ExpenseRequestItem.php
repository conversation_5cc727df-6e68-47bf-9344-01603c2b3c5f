<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ExpenseRequestItem extends Model
{
    use HasFactory;

    protected $table = 'expense_request_items';

    protected $fillable = [
        'expense_request_id',
        'expense_category_id',
        'description',
        'amount',
        'expense_date',
        'receipt_number',
        'receipt_file_path',
        'receipt_original_name',
        'account_id',
        'notes',
    ];

    protected $dates = ['expense_date'];

    protected $casts = [
        'expense_date' => 'date',
        'amount' => 'decimal:2',
    ];

    // Relationships
    public function expenseRequest()
    {
        return $this->belongsTo(ExpenseRequest::class);
    }

    public function expenseCategory()
    {
        return $this->belongsTo(ExpenseCategory::class);
    }

    public function account()
    {
        return $this->belongsTo(Akun::class, 'account_id');
    }

    // Helper methods
    public function getFormattedAmountAttribute()
    {
        return 'Rp ' . number_format($this->amount, 0, ',', '.');
    }

    public function getFormattedExpenseDateAttribute()
    {
        return $this->expense_date ? $this->expense_date->format('d/m/Y') : null;
    }

    public function hasReceipt()
    {
        return !empty($this->receipt_file_path);
    }

    public function getReceiptStatusAttribute()
    {
        if ($this->expenseCategory && $this->expenseCategory->requires_receipt) {
            return $this->hasReceipt() ? 'Complete' : 'Missing';
        }
        return $this->hasReceipt() ? 'Available' : 'Not Required';
    }

    public function getReceiptStatusColorAttribute()
    {
        return match ($this->receipt_status) {
            'Complete' => 'success',
            'Available' => 'success',
            'Missing' => 'danger',
            'Not Required' => 'gray',
            default => 'gray'
        };
    }

    public function validateLimits()
    {
        if (!$this->expenseCategory) return true;

        $dailyCheck = $this->expenseCategory->checkDailyLimit($this->amount, $this->expense_date);
        $monthlyCheck = $this->expenseCategory->checkMonthlyLimit(
            $this->amount,
            $this->expense_date->month,
            $this->expense_date->year
        );

        return $dailyCheck && $monthlyCheck;
    }

    public function getLimitViolationsAttribute()
    {
        if (!$this->expenseCategory) return [];

        $violations = [];

        if (!$this->expenseCategory->checkDailyLimit($this->amount, $this->expense_date)) {
            $violations[] = 'Daily limit exceeded';
        }

        if (!$this->expenseCategory->checkMonthlyLimit(
            $this->amount,
            $this->expense_date->month,
            $this->expense_date->year
        )) {
            $violations[] = 'Monthly limit exceeded';
        }

        return $violations;
    }

    // Auto-update parent totals when saving
    protected static function boot()
    {
        parent::boot();
    }
}
