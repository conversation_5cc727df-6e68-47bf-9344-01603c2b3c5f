<?php

namespace App\Filament\Manufacturing\Resources\QualityInspectionResource\Pages;

use App\Filament\Manufacturing\Resources\QualityInspectionResource;
use Filament\Resources\Pages\CreateRecord;

class CreateQualityInspection extends CreateRecord
{
    protected static string $resource = QualityInspectionResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('view', ['record' => $this->getRecord()]);
    }

    protected function afterCreate(): void
    {
        // Auto-calculate overall result based on quantities
        $this->record->calculateOverallResult();
    }
}
