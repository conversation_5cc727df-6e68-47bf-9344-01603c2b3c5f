<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CategoryProductVieraQuinosSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        include database_path('seeders/data/category_product_viera.php');

        $count = count($dataList);
        $bar = $this->command->getOutput()->createProgressBar($count);
        $bar->start();

        try {
            DB::transaction(function () use ($dataList, $bar) {
                foreach ($dataList as $data) {
                    if (!Category::where($data)->first()) {
                        Category::create($data);
                        $bar->advance();
                    }
                }
            });

            $bar->finish();
            $this->command->newLine();
            $this->command->info("✅ Selesai insert {$count} data Category Product.");
        } catch (\Throwable $e) {
            $bar->clear();
            $this->command->error("❌ Gagal insert data: " . $e->getMessage());
        }
    }
}
