<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('expense_request_items', function (Blueprint $table) {
            $table->string('receipt_original_name')->nullable()->after('receipt_file_path');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('expense_request_items', function (Blueprint $table) {
            $table->dropColumn('receipt_original_name');
        });
    }
};
