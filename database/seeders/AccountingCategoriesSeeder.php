<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\TransactionCategory;
use App\Models\TransactionSubcategory;
use App\Models\ExpenseCategory;
use App\Models\Akun;

class AccountingCategoriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Seed Transaction Categories
        $transactionCategories = [
            // Revenue Categories
            [
                'code' => 'REV001',
                'name' => 'Penjualan Produk',
                'type' => 'revenue',
                'description' => 'Pendapatan dari penjualan produk utama',
                'is_active' => true,
                'subcategories' => [
                    ['code' => 'REV001-01', 'name' => 'Penjualan Retail', 'description' => 'Penjualan langsung ke konsumen'],
                    ['code' => 'REV001-02', 'name' => 'Penjualan Grosir', 'description' => 'Penjualan dalam jumlah besar'],
                ]
            ],
            [
                'code' => 'REV002',
                'name' => 'Penjualan Jasa',
                'type' => 'revenue',
                'description' => 'Pendapatan dari penyediaan jasa',
                'is_active' => true,
                'subcategories' => [
                    ['code' => 'REV002-01', 'name' => 'Jasa Konsultasi', 'description' => 'Pendapatan dari konsultasi'],
                    ['code' => 'REV002-02', 'name' => 'Jasa Maintenance', 'description' => 'Pendapatan dari pemeliharaan'],
                ]
            ],
            [
                'code' => 'REV003',
                'name' => 'Pendapatan Lain-lain',
                'type' => 'revenue',
                'description' => 'Pendapatan di luar operasional utama',
                'is_active' => true,
                'subcategories' => [
                    ['code' => 'REV003-01', 'name' => 'Bunga Bank', 'description' => 'Pendapatan bunga dari bank'],
                    ['code' => 'REV003-02', 'name' => 'Sewa Aset', 'description' => 'Pendapatan dari penyewaan aset'],
                ]
            ],

            // Expense Categories
            [
                'code' => 'EXP001',
                'name' => 'Biaya Operasional',
                'type' => 'expense',
                'description' => 'Biaya untuk operasional harian',
                'is_active' => true,
                'subcategories' => [
                    ['code' => 'EXP001-01', 'name' => 'Listrik & Air', 'description' => 'Biaya utilitas'],
                    ['code' => 'EXP001-02', 'name' => 'Telepon & Internet', 'description' => 'Biaya komunikasi'],
                    ['code' => 'EXP001-03', 'name' => 'Sewa Kantor', 'description' => 'Biaya sewa tempat usaha'],
                ]
            ],
            [
                'code' => 'EXP002',
                'name' => 'Biaya Karyawan',
                'type' => 'expense',
                'description' => 'Biaya yang berkaitan dengan karyawan',
                'is_active' => true,
                'subcategories' => [
                    ['code' => 'EXP002-01', 'name' => 'Gaji Pokok', 'description' => 'Gaji dasar karyawan'],
                    ['code' => 'EXP002-02', 'name' => 'Tunjangan', 'description' => 'Tunjangan karyawan'],
                    ['code' => 'EXP002-03', 'name' => 'BPJS & Asuransi', 'description' => 'Biaya asuransi karyawan'],
                ]
            ],
            [
                'code' => 'EXP003',
                'name' => 'Biaya Pemasaran',
                'type' => 'expense',
                'description' => 'Biaya untuk kegiatan pemasaran',
                'is_active' => true,
                'subcategories' => [
                    ['code' => 'EXP003-01', 'name' => 'Iklan & Promosi', 'description' => 'Biaya iklan dan promosi'],
                    ['code' => 'EXP003-02', 'name' => 'Event & Pameran', 'description' => 'Biaya mengikuti event'],
                ]
            ],
        ];

        foreach ($transactionCategories as $categoryData) {
            $subcategories = $categoryData['subcategories'] ?? [];
            unset($categoryData['subcategories']);

            $category = TransactionCategory::create($categoryData);

            foreach ($subcategories as $subData) {
                TransactionSubcategory::create([
                    'category_id' => $category->id,
                    'code' => $subData['code'],
                    'name' => $subData['name'],
                    'description' => $subData['description'],
                    'is_active' => true,
                ]);
            }
        }

        // Seed Expense Categories with Account Mapping
        $expenseCategories = [
            [
                'code' => 'EXPCAT001',
                'name' => 'Biaya Transportasi',
                'description' => 'Biaya perjalanan dan transportasi',
                'default_account_code' => '********', // Travel Expense
                'daily_limit' => 500000,
                'monthly_limit' => ********,
                'requires_receipt' => true,
                'is_active' => true,
            ],
            [
                'code' => 'EXPCAT002',
                'name' => 'Biaya Konsumsi',
                'description' => 'Biaya makan dan minum',
                'default_account_code' => '********', // Meal Expense
                'daily_limit' => 200000,
                'monthly_limit' => 4000000,
                'requires_receipt' => true,
                'is_active' => true,
            ],
            [
                'code' => 'EXPCAT003',
                'name' => 'Biaya Alat Tulis',
                'description' => 'Biaya pembelian alat tulis kantor',
                'default_account_code' => '********', // Office Supplies
                'daily_limit' => null,
                'monthly_limit' => 2000000,
                'requires_receipt' => true,
                'is_active' => true,
            ],
            [
                'code' => 'EXPCAT004',
                'name' => 'Biaya Komunikasi',
                'description' => 'Biaya telepon, internet, dan komunikasi',
                'default_account_code' => '********', // Communication Expense
                'daily_limit' => null,
                'monthly_limit' => 3000000,
                'requires_receipt' => false,
                'is_active' => true,
            ],
            [
                'code' => 'EXPCAT005',
                'name' => 'Biaya Maintenance',
                'description' => 'Biaya pemeliharaan dan perbaikan',
                'default_account_code' => '********', // Maintenance Expense
                'daily_limit' => null,
                'monthly_limit' => null,
                'requires_receipt' => true,
                'is_active' => true,
            ],
        ];

        foreach ($expenseCategories as $expenseData) {
            $defaultAccount = null;
            if (isset($expenseData['default_account_code'])) {
                $defaultAccount = Akun::where('kode_akun', $expenseData['default_account_code'])->first();
                unset($expenseData['default_account_code']);
            }

            // Skip if no default account found and it's required
            if (!$defaultAccount) {
                // Use a generic expense account or skip
                $defaultAccount = Akun::where('kategori_akun', 'Beban')->first();
            }

            ExpenseCategory::create([
                ...$expenseData,
                'default_account_id' => $defaultAccount?->id,
                'created_by' => 1, // Assuming admin user ID is 1
            ]);
        }

        $this->command->info('✅ Transaction Categories seeded successfully');
        $this->command->info('✅ Transaction Subcategories seeded successfully');
        $this->command->info('✅ Expense Categories seeded successfully');
    }
}
