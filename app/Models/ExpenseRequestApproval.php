<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ExpenseRequestApproval extends Model
{
    use HasFactory;

    protected $fillable = [
        'expense_request_id',
        'action_by',
        'action_at',
        'status',
        'note',
        'step',
    ];

    protected $casts = [
        'action_at' => 'datetime',
    ];

    public function expenseRequest(): BelongsTo
    {
        return $this->belongsTo(ExpenseRequest::class, 'expense_request_id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'action_by');
    }
}
