<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\PostingRule;
use App\Models\PostingRuleEntry;
use App\Models\PostingRuleCondition;
use App\Models\PostingRuleMapping;
use App\Models\Akun;
use Carbon\Carbon;

class ExpenseRequestPostingRuleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Creating posting rules for Expense Request...');

        // Find required accounts
        $kas = Akun::where('kode_akun', 'LIKE', '1101%')->where('nama_akun', 'LIKE', '%Kas%')->first();
        $bank = Akun::where('kode_akun', 'LIKE', '1102%')->where('nama_akun', 'LIKE', '%Bank%')->first();

        if (!$kas) {
            $kas = Akun::create([
                'kode_akun' => '1101001',
                'nama_akun' => 'Kas Kecil',
                'kategori_akun' => 'Kas',
                'tipe_akun' => 'Aset',
                'saldo_awal' => 0,
                'created_by' => 1,
            ]);
        }

        if (!$bank) {
            $bank = Akun::create([
                'kode_akun' => '1102001',
                'nama_akun' => 'Bank BCA',
                'kategori_akun' => 'Bank',
                'tipe_akun' => 'Aset',
                'saldo_awal' => 0,
                'created_by' => 1,
            ]);
        }

        // Create posting rule for ExpenseRequest approval
        $rule = PostingRule::create([
            'rule_name' => 'Expense Request Approval - Journal Entry',
            'source_type' => 'ExpenseRequest',
            'trigger_condition' => ['status' => 'Approved'],
            'description' => 'Auto-generate journal for approved expense request: Dr. Expense Accounts (per item), Cr. Cash/Bank Account',
            'priority' => 1,
            'is_active' => true,
            'created_by' => 1,
        ]);

        $this->command->info('Created posting rule: ' . $rule->rule_name);

        // Create condition for approved status
        PostingRuleCondition::create([
            'posting_rule_id' => $rule->id,
            'field_name' => 'status',
            'operator' => '=',
            'value' => '"Approved"',
            'logical_operator' => 'AND',
            'group_number' => 1,
        ]);

        $this->command->info('Created condition for status = Approved');

        // Create mappings for expense accounts (debit side)
        // This will be handled dynamically based on expense request items
        PostingRuleMapping::create([
            'posting_rule_id' => $rule->id,
            'mapping_type' => 'debit',
            'account_source' => 'related',
            'account_field' => 'expenseRequestItems.account_id',
            'amount_source' => 'related',
            'amount_field' => 'expenseRequestItems.amount',
            'description_template' => 'Expense - {request_number} - {expenseRequestItems.description}',
            'sequence' => 1,
        ]);

        // Create mapping for cash/bank account (credit side)
        // This will use the cash_account_id from approval data
        PostingRuleMapping::create([
            'posting_rule_id' => $rule->id,
            'mapping_type' => 'credit',
            'account_source' => 'approval_data',
            'account_field' => 'cash_account_id',
            'amount_source' => 'field',
            'amount_field' => 'total_amount',
            'description_template' => 'Payment for Expense Request - {request_number}',
            'sequence' => 2,
        ]);

        $this->command->info('Created posting rule mappings for ExpenseRequest');
        $this->command->info('ExpenseRequest posting rules seeded successfully!');
    }
}
