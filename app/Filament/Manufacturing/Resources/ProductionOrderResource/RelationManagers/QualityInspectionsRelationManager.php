<?php

namespace App\Filament\Manufacturing\Resources\ProductionOrderResource\RelationManagers;

use App\Models\QualityControlPoint;
use App\Models\ProductionStage;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class QualityInspectionsRelationManager extends RelationManager
{
    protected static string $relationship = 'qualityInspections';

    protected static ?string $recordTitleAttribute = 'inspection_number';

    protected static ?string $title = 'Quality Inspections';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\TextInput::make('inspection_number')
                            ->label('Inspection Number')
                            ->disabled()
                            ->dehydrated(false),

                        Forms\Components\Select::make('quality_control_point_id')
                            ->label('Quality Control Point')
                            ->options(QualityControlPoint::active()->pluck('name', 'id'))
                            ->searchable()
                            ->required(),
                    ]),

                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\Select::make('production_stage_id')
                            ->label('Production Stage')
                            ->options(ProductionStage::where('production_order_id', $this->getOwnerRecord()->id)
                                ->pluck('stage_name', 'id'))
                            ->searchable(),

                        Forms\Components\Select::make('inspector_id')
                            ->label('Inspector')
                            ->options(User::all()->pluck('name', 'id'))
                            ->searchable()
                            ->required()
                            ->default(auth()->id()),
                    ]),

                Forms\Components\Grid::make(4)
                    ->schema([
                        Forms\Components\TextInput::make('quantity_inspected')
                            ->label('Quantity Inspected')
                            ->numeric()
                            ->required(),

                        Forms\Components\TextInput::make('quantity_passed')
                            ->label('Quantity Passed')
                            ->numeric()
                            ->default(0),

                        Forms\Components\TextInput::make('quantity_failed')
                            ->label('Quantity Failed')
                            ->numeric()
                            ->default(0),

                        Forms\Components\TextInput::make('quantity_rework')
                            ->label('Quantity Rework')
                            ->numeric()
                            ->default(0),
                    ]),

                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\Select::make('overall_result')
                            ->options([
                                'pending' => 'Pending',
                                'passed' => 'Passed',
                                'failed' => 'Failed',
                                'conditional_pass' => 'Conditional Pass',
                            ])
                            ->default('pending')
                            ->required(),

                        Forms\Components\DateTimePicker::make('inspection_date')
                            ->required()
                            ->default(now()),
                    ]),

                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\Toggle::make('requires_rework')
                            ->label('Requires Rework')
                            ->default(false),

                        Forms\Components\Toggle::make('is_final_inspection')
                            ->label('Final Inspection')
                            ->default(false),
                    ]),

                Forms\Components\Textarea::make('notes')
                    ->rows(2)
                    ->columnSpanFull(),

                Forms\Components\Textarea::make('corrective_actions')
                    ->label('Corrective Actions')
                    ->rows(2)
                    ->columnSpanFull(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('inspection_number')
            ->columns([
                Tables\Columns\TextColumn::make('inspection_number')
                    ->label('Inspection #')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('qualityControlPoint.name')
                    ->label('QC Point')
                    ->searchable()
                    ->wrap(),

                Tables\Columns\TextColumn::make('productionStage.stage_name')
                    ->label('Stage')
                    ->searchable(),

                Tables\Columns\TextColumn::make('quantity_inspected')
                    ->label('Inspected')
                    ->numeric(),

                Tables\Columns\TextColumn::make('quantity_passed')
                    ->label('Passed')
                    ->numeric(),

                Tables\Columns\TextColumn::make('quantity_failed')
                    ->label('Failed')
                    ->numeric(),

                Tables\Columns\TextColumn::make('quantity_rework')
                    ->label('Rework')
                    ->numeric(),

                Tables\Columns\TextColumn::make('overall_result')
                    ->label('Result')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'pending' => 'gray',
                        'passed' => 'success',
                        'failed' => 'danger',
                        'conditional_pass' => 'warning',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('inspector.name')
                    ->label('Inspector')
                    ->searchable(),

                Tables\Columns\TextColumn::make('inspection_date')
                    ->label('Date')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\IconColumn::make('requires_rework')
                    ->label('Rework')
                    ->boolean(),

                Tables\Columns\IconColumn::make('is_final_inspection')
                    ->label('Final')
                    ->boolean(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('overall_result')
                    ->options([
                        'pending' => 'Pending',
                        'passed' => 'Passed',
                        'failed' => 'Failed',
                        'conditional_pass' => 'Conditional Pass',
                    ]),

                Tables\Filters\TernaryFilter::make('requires_rework')
                    ->label('Requires Rework'),

                Tables\Filters\TernaryFilter::make('is_final_inspection')
                    ->label('Final Inspection'),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),

                Tables\Actions\Action::make('add_tests')
                    ->label('Add Tests')
                    ->icon('heroicon-o-beaker')
                    ->color('info')
                    ->url(fn ($record) => route('filament.manufacturing.resources.quality-inspections.view', $record)),

                Tables\Actions\Action::make('approve')
                    ->label('Approve')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->visible(fn ($record) => $record->overall_result === 'pending')
                    ->requiresConfirmation()
                    ->action(function ($record) {
                        $record->update(['overall_result' => 'passed']);
                    }),

                Tables\Actions\Action::make('reject')
                    ->label('Reject')
                    ->icon('heroicon-o-x-circle')
                    ->color('danger')
                    ->visible(fn ($record) => $record->overall_result === 'pending')
                    ->form([
                        Forms\Components\Textarea::make('rejection_reason')
                            ->label('Rejection Reason')
                            ->required()
                            ->rows(3),
                    ])
                    ->action(function ($record, array $data) {
                        $record->update([
                            'overall_result' => 'failed',
                            'corrective_actions' => $data['rejection_reason'],
                            'requires_rework' => true,
                        ]);
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('inspection_date', 'desc');
    }
}
