<?php

namespace App\Filament\Setting\Resources;

use App\Filament\Setting\Resources\EventManagerResource\Pages;
use App\Filament\Setting\Resources\EventManagerResource\RelationManagers;
use App\Models\EventManager;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class EventManagerResource extends Resource
{
    protected static ?string $model = EventManager::class;

    protected static ?string $navigationIcon = 'heroicon-o-star';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Event')
                    ->description('Buat event dan template pesannya.')
                    ->schema([
                        Forms\Components\TextInput::make('event_name')
                            ->label('Nama Event')
                            ->placeholder('Contoh: notifikasi_sesuatu')
                            ->required()
                            ->helperText('Nama event yang akan memicu sesuatu.'),

                        Forms\Components\TextInput::make('description')
                            ->label('Deskripsi')
                            ->required()
                            ->placeholder('Contoh: Untuk notifikasi sesuatu'),

                        Forms\Components\Textarea::make('message')
                            ->label('Template Pesan')
                            ->placeholder('Contoh: Halo, {penerima}!')
                            ->rows(7)
                            ->columnSpanFull()
                            ->required(),
                    ])->columns(2), // Membuat form menjadi 2 kolom
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('event_name')
                    ->label('Nama Event')
                    ->searchable()
                    ->sortable()
                    ->copyable()
                    ->weight('bold')
                    ->badge(),

                Tables\Columns\TextColumn::make('description')
                    ->label('Deskripsi')
                    ->searchable()
                    ->limit(50),

                Tables\Columns\TextColumn::make('message')
                    ->label('Template Pesan')
                    ->limit(50),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListEventManagers::route('/'),
            'create' => Pages\CreateEventManager::route('/create'),
            'edit' => Pages\EditEventManager::route('/{record}/edit'),
        ];
    }
}
