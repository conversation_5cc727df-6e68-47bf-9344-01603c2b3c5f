<?php

namespace Database\Seeders;

use App\Models\Customer;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CustomerVieraQuinosSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        include database_path('seeders/data/customer_viera.php');

        $count = count($dataList);
        $bar = $this->command->getOutput()->createProgressBar($count);
        $bar->start();

        try {
            DB::transaction(function () use ($dataList, $bar) {
                foreach ($dataList as $data) {
                    Customer::create($data);
                    $bar->advance();
                }
            });

            $bar->finish();
            $this->command->newLine();
            $this->command->info("✅ Selesai insert {$count} data Customer.");
        } catch (\Throwable $e) {
            $bar->clear();
            $this->command->error("❌ Gagal insert data: " . $e->getMessage());
        }
    }
}
