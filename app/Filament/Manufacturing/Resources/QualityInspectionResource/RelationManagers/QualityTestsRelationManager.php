<?php

namespace App\Filament\Manufacturing\Resources\QualityInspectionResource\RelationManagers;

use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class QualityTestsRelationManager extends RelationManager
{
    protected static string $relationship = 'qualityTests';

    protected static ?string $recordTitleAttribute = 'test_name';

    protected static ?string $title = 'Quality Tests';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\TextInput::make('test_name')
                            ->label('Test Name')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\Select::make('test_type')
                            ->options([
                                'measurement' => 'Measurement',
                                'visual' => 'Visual',
                                'functional' => 'Functional',
                                'chemical' => 'Chemical',
                                'physical' => 'Physical',
                            ])
                            ->default('measurement')
                            ->required(),
                    ]),

                Forms\Components\Textarea::make('test_description')
                    ->label('Test Description')
                    ->rows(2)
                    ->columnSpanFull(),

                Forms\Components\Grid::make(4)
                    ->schema([
                        Forms\Components\TextInput::make('measured_value')
                            ->label('Measured Value')
                            ->numeric()
                            ->step(0.0001),

                        Forms\Components\TextInput::make('target_value')
                            ->label('Target Value')
                            ->numeric()
                            ->step(0.0001),

                        Forms\Components\TextInput::make('min_acceptable')
                            ->label('Min Acceptable')
                            ->numeric()
                            ->step(0.0001),

                        Forms\Components\TextInput::make('max_acceptable')
                            ->label('Max Acceptable')
                            ->numeric()
                            ->step(0.0001),
                    ]),

                Forms\Components\Grid::make(3)
                    ->schema([
                        Forms\Components\TextInput::make('unit_of_measure')
                            ->label('Unit of Measure')
                            ->maxLength(20),

                        Forms\Components\Select::make('result')
                            ->options([
                                'pass' => 'Pass',
                                'fail' => 'Fail',
                                'warning' => 'Warning',
                            ])
                            ->default('pass')
                            ->required(),

                        Forms\Components\Select::make('tested_by')
                            ->label('Tested By')
                            ->options(User::all()->pluck('name', 'id'))
                            ->searchable()
                            ->required()
                            ->default(auth()->id()),
                    ]),

                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\TextInput::make('equipment_used')
                            ->label('Equipment Used')
                            ->maxLength(255),

                        Forms\Components\DateTimePicker::make('test_date')
                            ->required()
                            ->default(now()),
                    ]),

                Forms\Components\Textarea::make('observations')
                    ->rows(3)
                    ->columnSpanFull(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('test_name')
            ->columns([
                Tables\Columns\TextColumn::make('test_name')
                    ->label('Test')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('test_type')
                    ->label('Type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'measurement' => 'primary',
                        'visual' => 'info',
                        'functional' => 'warning',
                        'chemical' => 'success',
                        'physical' => 'secondary',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('measured_value')
                    ->label('Measured')
                    ->numeric(decimalPlaces: 4),

                Tables\Columns\TextColumn::make('target_value')
                    ->label('Target')
                    ->numeric(decimalPlaces: 4),

                Tables\Columns\TextColumn::make('min_acceptable')
                    ->label('Min')
                    ->numeric(decimalPlaces: 4),

                Tables\Columns\TextColumn::make('max_acceptable')
                    ->label('Max')
                    ->numeric(decimalPlaces: 4),

                Tables\Columns\TextColumn::make('unit_of_measure')
                    ->label('Unit'),

                Tables\Columns\TextColumn::make('result')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'pass' => 'success',
                        'fail' => 'danger',
                        'warning' => 'warning',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('tester.name')
                    ->label('Tested By')
                    ->searchable(),

                Tables\Columns\TextColumn::make('test_date')
                    ->label('Date')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('equipment_used')
                    ->label('Equipment')
                    ->wrap(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('test_type')
                    ->options([
                        'measurement' => 'Measurement',
                        'visual' => 'Visual',
                        'functional' => 'Functional',
                        'chemical' => 'Chemical',
                        'physical' => 'Physical',
                    ]),

                Tables\Filters\SelectFilter::make('result')
                    ->options([
                        'pass' => 'Pass',
                        'fail' => 'Fail',
                        'warning' => 'Warning',
                    ]),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->mutateFormDataUsing(function (array $data): array {
                        // Auto-evaluate result based on measured value and limits
                        if (isset($data['measured_value'])) {
                            $measured = $data['measured_value'];
                            $min = $data['min_acceptable'] ?? null;
                            $max = $data['max_acceptable'] ?? null;

                            if (($min !== null && $measured < $min) || ($max !== null && $measured > $max)) {
                                $data['result'] = 'fail';
                            } else {
                                $data['result'] = 'pass';
                            }
                        }
                        return $data;
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),

                Tables\Actions\Action::make('retest')
                    ->label('Retest')
                    ->icon('heroicon-o-arrow-path')
                    ->color('warning')
                    ->form([
                        Forms\Components\TextInput::make('new_measured_value')
                            ->label('New Measured Value')
                            ->numeric()
                            ->step(0.0001)
                            ->required(),

                        Forms\Components\Textarea::make('retest_reason')
                            ->label('Retest Reason')
                            ->required()
                            ->rows(2),
                    ])
                    ->action(function ($record, array $data) {
                        $record->update([
                            'measured_value' => $data['new_measured_value'],
                            'observations' => $record->observations . "\n\nRetest: " . $data['retest_reason'],
                            'test_date' => now(),
                        ]);
                        
                        // Re-evaluate result
                        $record->evaluateResult();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('test_date', 'desc');
    }
}
