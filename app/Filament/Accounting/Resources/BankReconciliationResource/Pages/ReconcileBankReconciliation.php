<?php

namespace App\Filament\Accounting\Resources\BankReconciliationResource\Pages;

use App\Filament\Accounting\Resources\BankReconciliationResource;
use App\Models\BankReconciliationItem;
use App\Models\JournalEntry;
use Filament\Actions;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\Page;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class ReconcileBankReconciliation extends Page
{
    protected static string $resource = BankReconciliationResource::class;

    protected static string $view = 'filament.accounting.pages.reconcile-bank-reconciliation';

    public $record;

    public function mount($record): void
    {
        $this->record = $this->getResource()::resolveRecordRouteBinding($record);
        
        // Update status to in_progress if still draft
        if ($this->record->status === 'draft') {
            $this->record->update(['status' => 'in_progress']);
        }
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('addStatementItem')
                ->label('Tambah Item Statement')
                ->icon('heroicon-o-plus')
                ->color('info')
                ->form([
                    Forms\Components\DatePicker::make('transaction_date')
                        ->label('Tanggal Transaksi')
                        ->required()
                        ->native(false),
                    Forms\Components\TextInput::make('description')
                        ->label('Deskripsi')
                        ->required()
                        ->maxLength(255),
                    Forms\Components\TextInput::make('amount')
                        ->label('Jumlah')
                        ->required()
                        ->numeric()
                        ->prefix('Rp'),
                    Forms\Components\TextInput::make('statement_reference')
                        ->label('Referensi Statement')
                        ->maxLength(255),
                    Forms\Components\Textarea::make('notes')
                        ->label('Catatan')
                        ->maxLength(500),
                ])
                ->action(function (array $data) {
                    BankReconciliationItem::create([
                        'bank_reconciliation_id' => $this->record->id,
                        'transaction_date' => $data['transaction_date'],
                        'description' => $data['description'],
                        'amount' => $data['amount'],
                        'statement_reference' => $data['statement_reference'] ?? null,
                        'notes' => $data['notes'] ?? null,
                        'type' => 'statement',
                        'status' => 'unmatched',
                    ]);

                    Notification::make()
                        ->title('Item statement berhasil ditambahkan')
                        ->success()
                        ->send();
                }),

            Actions\Action::make('loadBookEntries')
                ->label('Muat Entri Buku')
                ->icon('heroicon-o-document-text')
                ->color('warning')
                ->action(function () {
                    $this->loadBookEntries();
                }),

            Actions\Action::make('autoMatch')
                ->label('Auto Match')
                ->icon('heroicon-o-link')
                ->color('success')
                ->action(function () {
                    $this->autoMatchItems();
                }),

            Actions\Action::make('completeReconciliation')
                ->label('Selesaikan Rekonsiliasi')
                ->icon('heroicon-o-check-circle')
                ->color('success')
                ->requiresConfirmation()
                ->visible(fn () => $this->record->canBeCompleted())
                ->action(function () {
                    $this->completeReconciliation();
                }),
        ];
    }

    public function loadBookEntries(): void
    {
        // Load journal entries for the account within the reconciliation period
        $journalEntries = JournalEntry::whereHas('account', function ($query) {
                $query->where('id', $this->record->account_id);
            })
            ->whereHas('journal', function ($query) {
                $query->whereBetween('transaction_date', [
                    $this->record->reconciliation_date->subDays(30),
                    $this->record->statement_date
                ]);
            })
            ->whereDoesntHave('bankReconciliationItems')
            ->get();

        foreach ($journalEntries as $entry) {
            BankReconciliationItem::create([
                'bank_reconciliation_id' => $this->record->id,
                'journal_entry_id' => $entry->id,
                'transaction_date' => $entry->journal->transaction_date,
                'description' => $entry->description,
                'amount' => $entry->debit > 0 ? $entry->debit : -$entry->credit,
                'type' => 'book',
                'status' => 'unmatched',
            ]);
        }

        Notification::make()
            ->title('Entri buku berhasil dimuat')
            ->success()
            ->send();
    }

    public function autoMatchItems(): void
    {
        $statementItems = $this->record->items()->where('type', 'statement')->where('status', 'unmatched')->get();
        $bookItems = $this->record->items()->where('type', 'book')->where('status', 'unmatched')->get();

        $matchedCount = 0;

        foreach ($statementItems as $statementItem) {
            foreach ($bookItems as $bookItem) {
                // Match by amount and date (within 3 days)
                if (abs($statementItem->amount - $bookItem->amount) < 0.01 && 
                    abs($statementItem->transaction_date->diffInDays($bookItem->transaction_date)) <= 3) {
                    
                    $statementItem->matchWith($bookItem);
                    $matchedCount++;
                    break;
                }
            }
        }

        Notification::make()
            ->title("Auto match selesai: {$matchedCount} item berhasil dicocokkan")
            ->success()
            ->send();
    }

    public function completeReconciliation(): void
    {
        $this->record->update([
            'status' => 'completed',
            'reconciled_by' => auth()->id(),
            'reconciled_at' => now(),
        ]);

        // Mark all matched items as reconciled
        $this->record->items()->where('status', 'matched')->update(['status' => 'reconciled']);

        Notification::make()
            ->title('Rekonsiliasi berhasil diselesaikan')
            ->success()
            ->send();

        $this->redirect($this->getResource()::getUrl('index'));
    }

    public function getTitle(): string
    {
        return "Rekonsiliasi Bank - {$this->record->reconciliation_number}";
    }
}
