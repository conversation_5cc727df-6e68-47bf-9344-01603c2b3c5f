<?php

namespace App\Models;

use App\Services\ExpenseRequestService;
use App\Services\PermissionService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class ExpenseRequest extends Model
{
    use HasFactory, SoftDeletes;

    const APPROVAL_PERMISSION_PREFIX = 'approve_expense_request_';

    protected $table = 'expense_requests';

    protected $fillable = [
        'request_number',
        'request_date',
        'employee_id',
        'entitas_id',
        'expense_type',
        'purpose',
        'notes',
        'requested_by',
    ];

    protected $casts = [
        'request_date' => 'date',
    ];

    // Relationships
    public function employee()
    {
        return $this->belongsTo(Karyawan::class, 'employee_id');
    }

    public function entitas()
    {
        return $this->belongsTo(Entitas::class);
    }

    public function expenseRequestItems()
    {
        return $this->hasMany(ExpenseRequestItem::class);
    }

    public function cashDisbursements()
    {
        return $this->hasMany(CashDisbursement::class);
    }

    public function requestedBy()
    {
        return $this->belongsTo(User::class, 'requested_by');
    }

    public function approvals()
    {
        return $this->hasMany(ExpenseRequestApproval::class, 'expense_request_id')->latest();
    }

    public function approvalsNoRevision()
    {
        return $this->hasMany(ExpenseRequestApproval::class, 'expense_request_id')->latest()->whereNot('status', 'Revision');
    }

    // Scopes
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByEmployee($query, $employeeId)
    {
        return $query->where('employee_id', $employeeId);
    }

    public function scopeByEntitas($query, $entitasId)
    {
        return $query->where('entitas_id', $entitasId);
    }

    public function scopeByExpenseType($query, $type)
    {
        return $query->where('expense_type', $type);
    }

    // Helper methods
    public function getTotalItemsAttribute()
    {
        return $this->expenseRequestItems()->count();
    }

    public function getStatusAttribute()
    {
        return $this->approvals()->first()->status;
    }


    public function getStatusColorAttribute()
    {
        return match ($this->getAttribute('status')) {
            'Draft' => 'gray',
            'Submitted' => 'warning',
            'Approved' => 'success',
            'Rejected' => 'danger',
            'Paid' => 'success',
            'Cancelled' => 'danger',
            default => 'gray'
        };
    }

    public function getStatusLabelAttribute()
    {
        return match ($this->getAttribute('status')) {
            'Draft' => 'Draft',
            'Submitted' => 'Menunggu Persetujuan',
            'Approved' => 'Disetujui',
            'Rejected' => 'Ditolak',
            'Paid' => 'Dibayar',
            'Cancelled' => 'Dibatalkan',
            default => $this->getAttribute('status')
        };
    }

    public function getExpenseTypeLabelAttribute()
    {
        return match ($this->expense_type) {
            'Petty_Cash' => 'Kas Kecil',
            'Reimbursement' => 'Reimbursement',
            'Advance' => 'Uang Muka',
            default => $this->expense_type
        };
    }

    public function getExpenseTypeColorAttribute()
    {
        return match ($this->expense_type) {
            'Petty_Cash' => 'info',
            'Reimbursement' => 'success',
            'Advance' => 'warning',
            default => 'gray'
        };
    }

    public function isEditable()
    {
        return in_array($this->getAttribute('status'), ['Draft', 'Revision']);
    }

    public function canBeSubmitted()
    {
        return $this->getAttribute('status') === 'Draft' && $this->expenseRequestItems()->count() > 0;
    }

    public function canBeApproved()
    {
        return in_array($this->getAttribute('status'), ['Submitted', 'Revision']);
    }

    public function canApproving()
    {
        $step = $this->approvals()->first();
        if (!$step) return false;

        $step = $step->step;
        return $this->canBeApproved() && (PermissionService::hasPermission(self::APPROVAL_PERMISSION_PREFIX . $step, null));
    }

    public function canBeRejected()
    {
        return in_array($this->getAttribute('status'), ['Submitted', 'Revision']);
    }

    public function canRejecting()
    {
        $step = $this->approvals()->first();
        if (!$step) return false;

        $step = $step->step;
        return $this->canBeRejected() && (PermissionService::hasPermission(self::APPROVAL_PERMISSION_PREFIX . $step, null));
    }

    public function canBeRevised()
    {
        return in_array($this->getAttribute('status'), ['Submitted', 'Revision']);
    }

    public function canRevising()
    {
        $step = $this->approvals()->first();
        if (!$step) return false;

        $step = $step->step;
        return $this->canBeRevised() && (PermissionService::hasPermission(self::APPROVAL_PERMISSION_PREFIX . $step, null));
    }

    public function canBeCancelled()
    {
        return in_array($this->getAttribute('status'), ['Draft', 'Submitted']);
    }

    public function canBePaid()
    {
        return $this->getAttribute('status') === 'Approved';
    }

    public function submit($step = 1)
    {
        if (!$this->canBeSubmitted() && !$this->canBeApproved()) {
            throw new \Exception('Expense request cannot be submitted');
        }

        if (!(KaryawanPermissionType::where('name', self::APPROVAL_PERMISSION_PREFIX . $step)->first())) {
            $this->approve();
            return;
        }

        $approval = new ExpenseRequestApproval();
        $approval->status = 'Submitted';
        $approval->action_by = auth()->id();
        $approval->action_at = Carbon::now();
        $approval->expense_request_id = $this->id;
        $approval->step = $step;
        $approval->save();
    }

    public function approve()
    {
        if (!$this->canBeApproved()) {
            throw new \Exception('Expense request cannot be approved');
        }

        $approval = new ExpenseRequestApproval();
        $approval->status = 'Approved';
        $approval->action_by = auth()->id();
        $approval->action_at = Carbon::now();
        $approval->expense_request_id = $this->id;
        $approval->save();
    }

    public function reject($reason = null)
    {
        if (!$this->canBeRejected()) {
            throw new \Exception('Expense request cannot be rejected');
        }

        $approval = new ExpenseRequestApproval();
        $approval->status = 'Rejected';
        $approval->action_by = auth()->id();
        $approval->action_at = Carbon::now();
        $approval->expense_request_id = $this->id;
        $approval->note = $reason;
        $approval->save();
    }

    public function revision($note = null)
    {
        if (!$this->canBeRevised()) {
            throw new \Exception('Expense request cannot be revised');
        }

        $approval = new ExpenseRequestApproval();
        $approval->status = 'Revision';
        $approval->action_by = auth()->id();
        $approval->action_at = Carbon::now();
        $approval->expense_request_id = $this->id;
        $approval->note = $note;
        $approval->step = $this->approvals()->first()->step;
        $approval->save();
    }

    public function pay($paymentMethodId)
    {
        if (!$this->canBePaid()) {
            throw new \Exception('Expense request cannot be paid');
        }

        $paymentMethod = PaymentMethod::find($paymentMethodId);

        $approval = new ExpenseRequestApproval();
        $approval->status = 'Paid';
        $approval->action_by = auth()->id();
        $approval->action_at = Carbon::now();
        $approval->expense_request_id = $this->id;
        $approval->note = 'Pembayaran via ' . $paymentMethod->method_display_name;
        $approval->save();

        // // Create cash disbursement for payment processing
        // if (!empty($this->approvals())) {
        //     $this->createCashDisbursement($this->approvals());
        // }

        // Create journal entry for approved expense request
        $this->createAutoJournaling($paymentMethod);
    }


    public function getTotalAmountAttribute()
    {
        return $this->expenseRequestItems()->sum('amount');
    }

    protected function createCashDisbursement($approvalData)
    {
        $disbursement = CashDisbursement::create([
            'disbursement_number' => CashDisbursement::generateDisbursementNumber(),
            'disbursement_date' => Carbon::now()->toDateString(),
            'expense_request_id' => $this->id,
            'payee_employee_id' => $this->employee_id,
            'amount' => $this->getAttribute('total_amount'),
            'payment_method' => $approvalData['payment_method'] ?? 'Cash',
            'cash_account_id' => $approvalData['cash_account_id'] ?? null,
            'status' => 'Draft',
            'purpose' => $this->purpose,
            'notes' => $approvalData['approval_notes'] ?? null,
            'created_by' => auth()->id(),
        ]);

        // Create disbursement items from expense request items
        foreach ($this->expenseRequestItems as $item) {
            $disbursement->cashDisbursementItems()->create([
                'expense_category_id' => $item->expense_category_id,
                'description' => $item->description,
                'amount' => $item->amount,
                'account_id' => $item->account_id,
            ]);
        }

        return $disbursement;
    }

    protected function createJournalEntry()
    {
        // Use JournalingService to create journal entries
        try {
            $journalingService = app(\App\Services\JournalingService::class);
            $journalingService->postTransaction('ExpenseRequest', $this);
        } catch (\Exception $e) {
            // Log error but don't fail the approval
            Log::error('Failed to create journal entry for ExpenseRequest: ' . $e->getMessage());
        }
    }

    protected function createAutoJournaling(PaymentMethod $paymentMethod)
    {
        try {
            $jId = Journal::create([
                'source_type' => 'ExpenseRequest',
                'reference_number' => $this->request_number,
                'description' => 'expense request - ' . $this->request_number,
                'status' => 'Posted',
                'transaction_date' => Carbon::now(),
                'created_by' => auth()->id(),
            ]);

            $expenseItems = $this->expenseRequestItems()->get();
            $urutan = 1;
            foreach ($expenseItems as $key => $item) {
                JournalEntry::create([
                    'journal_id' => $jId->id,
                    'account_id' => $item->account_id,
                    'description' => $item->description . ' - ' . $this->request_number,
                    'debit' => $item->amount,
                    'credit' => 0,
                    'sort_order' => $urutan++,
                ]);
            }
            JournalEntry::create([
                'journal_id' => $jId->id,
                'account_id' => $paymentMethod->akun_id,
                'description' => 'Pembayaran ' . $this->request_number . ' via ' . $paymentMethod->method_display_name,
                'credit' => $item->amount,
                'debit' => 0,
                'sort_order' => $urutan,
            ]);
        } catch (\Exception $e) {
            // Log error but don't fail the approval
            Log::error('Failed to create auto journal for ExpenseRequest: ' . $e->getMessage());
        }
    }

    public function getFormattedRequestDateAttribute()
    {
        return $this->request_date ? $this->request_date->format('d/m/Y') : null;
    }

    public function getFormattedTotalAmountAttribute()
    {
        return 'Rp ' . number_format((float)$this->getAttribute('total_amount'), 0, ',', '.');
    }

    // Auto-generate request number
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($expenseRequest) {
            $expenseRequest->request_date = now()->format('Y-m-d');
            if (empty($expenseRequest->request_number)) {
                $expenseRequest->request_number = static::generateRequestNumber();
            }
        });
    }

    public static function generateRequestNumber()
    {
        $prefix = 'ER';
        $date = Carbon::now()->format('Ymd');
        $lastRequest = static::withTrashed()
            ->where('request_number', 'like', $prefix . $date . '%')
            ->orderBy('request_number', 'desc')
            ->first();

        if ($lastRequest) {
            $lastNumber = intval(substr($lastRequest->request_number, -4));
            $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
        } else {
            $newNumber = '0001';
        }

        return $prefix . $date . $newNumber;
    }

    // Auto-generate request number
    protected static function booted()
    {
        static::created(function (ExpenseRequest $expenseRequest) {
            $approval = new ExpenseRequestApproval();
            $approval->status = 'Draft';
            $approval->action_by = auth()->id();
            $approval->action_at = Carbon::now();
            $approval->expense_request_id = $expenseRequest->id;
            $approval->save();

            $eventManager = \App\Models\EventManager::where('event_name', 'expense_request_created')->first();
            if ($eventManager) {
                resolve(ExpenseRequestService::class)->handleCreationNotification($expenseRequest, $eventManager->id);
            }
        });
    }
}
