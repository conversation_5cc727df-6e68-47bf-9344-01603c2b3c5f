<?php

namespace App\Providers\Filament;

use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\AuthenticateSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Widgets;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;

class SettingPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        return $panel
            ->id('setting')
            ->path('setting')
            ->login()
            ->colors([
                'primary' => Color::Yellow,
            ])
            ->viteTheme('resources/css/filament/admin/theme.css')
            ->discoverResources(in: app_path('Filament/Setting/Resources'), for: 'App\\Filament\\Setting\\Resources')
            ->discoverPages(in: app_path('Filament/Setting/Pages'), for: 'App\\Filament\\Setting\\Pages')
            ->discoverWidgets(in: app_path('Filament/Setting/Widgets'), for: 'App\\Filament\\Setting\\Widgets')
            ->pages([
                Pages\Dashboard::class,
            ])
            ->widgets([])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ])
            ->brandName('PT. Viera Anugrah Pertama')
            ->brandLogo(fn() => view('filament.admin.logo'))
            ->favicon(asset('images/viera-logo.png'))
            ->maxContentWidth('full')
            ->renderHook(
                \Filament\View\PanelsRenderHook::SIDEBAR_NAV_START,
                fn() => view('filament.components.panel-switcher-sidebar')
            )
            ->sidebarCollapsibleOnDesktop();
    }
}
