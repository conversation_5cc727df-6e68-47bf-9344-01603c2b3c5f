<?php

namespace App\Filament\Manufacturing\Resources\ProductionOrderResource\RelationManagers;

use App\Models\Product;
use App\Models\Warehouse;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class ProductionOrderMaterialsRelationManager extends RelationManager
{
    protected static string $relationship = 'productionOrderMaterials';

    protected static ?string $recordTitleAttribute = 'product.name';

    protected static ?string $title = 'Required Materials';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\Select::make('product_id')
                            ->label('Material/Component')
                            ->options(Product::all()->pluck('name', 'id'))
                            ->searchable()
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set) {
                                if ($state) {
                                    $product = Product::find($state);
                                    if ($product) {
                                        $set('unit', $product->unit ?? 'pcs');
                                        $set('unit_cost', $product->cost ?? 0);
                                    }
                                }
                            }),

                        Forms\Components\Select::make('warehouse_id')
                            ->label('Warehouse')
                            ->options(Warehouse::all()->pluck('name', 'id'))
                            ->searchable()
                            ->required(),
                    ]),

                Forms\Components\Grid::make(3)
                    ->schema([
                        Forms\Components\TextInput::make('required_quantity')
                            ->label('Required Quantity')
                            ->numeric()
                            ->step(0.0001)
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                $unitCost = $get('unit_cost') ?? 0;
                                $set('total_cost', $state * $unitCost);
                            }),

                        Forms\Components\TextInput::make('allocated_quantity')
                            ->label('Allocated Quantity')
                            ->numeric()
                            ->step(0.0001)
                            ->default(0),

                        Forms\Components\TextInput::make('consumed_quantity')
                            ->label('Consumed Quantity')
                            ->numeric()
                            ->step(0.0001)
                            ->default(0),
                    ]),

                Forms\Components\Grid::make(3)
                    ->schema([
                        Forms\Components\TextInput::make('unit')
                            ->required(),

                        Forms\Components\TextInput::make('unit_cost')
                            ->label('Unit Cost')
                            ->numeric()
                            ->prefix('Rp')
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                $quantity = $get('required_quantity') ?? 0;
                                $set('total_cost', $quantity * $state);
                            }),

                        Forms\Components\TextInput::make('total_cost')
                            ->label('Total Cost')
                            ->numeric()
                            ->prefix('Rp')
                            ->disabled()
                            ->dehydrated(),
                    ]),

                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\DatePicker::make('required_date')
                            ->required(),

                        Forms\Components\Toggle::make('is_critical')
                            ->label('Critical Material')
                            ->default(false),
                    ]),

                Forms\Components\Textarea::make('notes')
                    ->rows(2)
                    ->columnSpanFull(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('product.name')
            ->columns([
                Tables\Columns\TextColumn::make('product.sku')
                    ->label('Product Code')
                    ->searchable(),

                Tables\Columns\TextColumn::make('product.name')
                    ->label('Material')
                    ->searchable()
                    ->wrap(),

                Tables\Columns\TextColumn::make('required_quantity')
                    ->label('Required')
                    ->numeric(decimalPlaces: 4),

                Tables\Columns\TextColumn::make('allocated_quantity')
                    ->label('Allocated')
                    ->numeric(decimalPlaces: 4),

                Tables\Columns\TextColumn::make('consumed_quantity')
                    ->label('Consumed')
                    ->numeric(decimalPlaces: 4),

                Tables\Columns\TextColumn::make('unit'),

                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'pending' => 'gray',
                        'allocated' => 'warning',
                        'partially_consumed' => 'info',
                        'fully_consumed' => 'success',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('unit_cost')
                    ->label('Unit Cost')
                    ->money('IDR'),

                Tables\Columns\TextColumn::make('total_cost')
                    ->label('Total Cost')
                    ->money('IDR'),

                Tables\Columns\IconColumn::make('is_critical')
                    ->label('Critical')
                    ->boolean(),

                Tables\Columns\TextColumn::make('required_date')
                    ->date(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'pending' => 'Pending',
                        'allocated' => 'Allocated',
                        'partially_consumed' => 'Partially Consumed',
                        'fully_consumed' => 'Fully Consumed',
                    ]),

                Tables\Filters\TernaryFilter::make('is_critical')
                    ->label('Critical Materials'),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                
                Tables\Actions\Action::make('allocate')
                    ->label('Allocate')
                    ->icon('heroicon-o-check')
                    ->color('warning')
                    ->visible(fn ($record) => $record->status === 'pending')
                    ->form([
                        Forms\Components\TextInput::make('quantity_to_allocate')
                            ->label('Quantity to Allocate')
                            ->numeric()
                            ->required(),
                    ])
                    ->action(function ($record, array $data) {
                        $record->allocate($data['quantity_to_allocate']);
                    }),

                Tables\Actions\Action::make('consume')
                    ->label('Consume')
                    ->icon('heroicon-o-minus')
                    ->color('success')
                    ->visible(fn ($record) => in_array($record->status, ['allocated', 'partially_consumed']))
                    ->form([
                        Forms\Components\TextInput::make('quantity_to_consume')
                            ->label('Quantity to Consume')
                            ->numeric()
                            ->required(),
                    ])
                    ->action(function ($record, array $data) {
                        $record->consume($data['quantity_to_consume']);
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
