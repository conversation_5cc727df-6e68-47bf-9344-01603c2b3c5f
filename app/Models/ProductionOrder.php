<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProductionOrder extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'po_number',
        'production_plan_id',
        'bom_id',
        'product_id',
        'warehouse_id',
        'planned_quantity',
        'actual_quantity',
        'unit',
        'status',
        'priority',
        'planned_start_date',
        'planned_end_date',
        'actual_start_date',
        'actual_end_date',
        'planned_material_cost',
        'actual_material_cost',
        'planned_labor_cost',
        'actual_labor_cost',
        'planned_overhead_cost',
        'actual_overhead_cost',
        'total_planned_cost',
        'total_actual_cost',
        'planned_duration_hours',
        'actual_duration_hours',
        'yield_percentage',
        'assigned_to',
        'created_by',
        'notes',
    ];

    protected $casts = [
        'planned_quantity' => 'decimal:2',
        'actual_quantity' => 'decimal:2',
        'planned_start_date' => 'date',
        'planned_end_date' => 'date',
        'actual_start_date' => 'date',
        'actual_end_date' => 'date',
        'planned_material_cost' => 'decimal:2',
        'actual_material_cost' => 'decimal:2',
        'planned_labor_cost' => 'decimal:2',
        'actual_labor_cost' => 'decimal:2',
        'planned_overhead_cost' => 'decimal:2',
        'actual_overhead_cost' => 'decimal:2',
        'total_planned_cost' => 'decimal:2',
        'total_actual_cost' => 'decimal:2',
        'yield_percentage' => 'decimal:2',
    ];

    // Relationships
    public function productionPlan(): BelongsTo
    {
        return $this->belongsTo(ProductionPlan::class);
    }

    public function bom(): BelongsTo
    {
        return $this->belongsTo(Bom::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function warehouse(): BelongsTo
    {
        return $this->belongsTo(Warehouse::class);
    }

    public function assignedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function productionOrderMaterials(): HasMany
    {
        return $this->hasMany(ProductionOrderMaterial::class);
    }

    public function productionStages(): HasMany
    {
        return $this->hasMany(ProductionStage::class);
    }

    public function qualityInspections(): HasMany
    {
        return $this->hasMany(QualityInspection::class);
    }

    public function materialConsumptions(): HasMany
    {
        return $this->hasMany(MaterialConsumption::class);
    }

    public function productionSchedules(): HasMany
    {
        return $this->hasMany(ProductionSchedule::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->whereIn('status', ['planned', 'released', 'in_progress']);
    }

    public function scopeInProgress($query)
    {
        return $query->where('status', 'in_progress');
    }

    public function scopeForProduct($query, $productId)
    {
        return $query->where('product_id', $productId);
    }

    // Accessors
    public function getProgressPercentageAttribute(): float
    {
        if ($this->planned_quantity == 0) {
            return 0;
        }
        return ($this->actual_quantity / $this->planned_quantity) * 100;
    }

    public function getCostVarianceAttribute(): float
    {
        return $this->total_actual_cost - $this->total_planned_cost;
    }

    public function getScheduleVarianceAttribute(): int
    {
        if (!$this->actual_end_date || !$this->planned_end_date) {
            return 0;
        }
        return $this->actual_end_date->diffInDays($this->planned_end_date, false);
    }

    // Methods
    public function release(): void
    {
        $this->update(['status' => 'released']);
        $this->generateMaterialRequirements();
        $this->generateProductionStages();
    }

    public function start(): void
    {
        $this->update([
            'status' => 'in_progress',
            'actual_start_date' => now(),
        ]);
    }

    public function complete(): void
    {
        $this->update([
            'status' => 'completed',
            'actual_end_date' => now(),
        ]);
        
        $this->updateInventoryStock();
    }

    public function generateMaterialRequirements(): void
    {
        foreach ($this->bom->bomItems as $bomItem) {
            $this->productionOrderMaterials()->create([
                'product_id' => $bomItem->component_product_id,
                'required_quantity' => $bomItem->actual_quantity_required * $this->planned_quantity,
                'unit' => $bomItem->unit,
                'unit_cost' => $bomItem->unit_cost,
                'total_cost' => $bomItem->total_cost * $this->planned_quantity,
                'is_critical' => $bomItem->is_critical,
                'required_date' => $this->planned_start_date,
                'warehouse_id' => $this->warehouse_id,
            ]);
        }
    }

    public function generateProductionStages(): void
    {
        // Basic stages - can be customized based on product type
        $stages = [
            ['name' => 'Material Preparation', 'sequence' => 1, 'duration' => 60],
            ['name' => 'Production', 'sequence' => 2, 'duration' => 240],
            ['name' => 'Quality Control', 'sequence' => 3, 'duration' => 30],
            ['name' => 'Packaging', 'sequence' => 4, 'duration' => 60],
        ];

        $startTime = $this->planned_start_date->setTime(8, 0);
        
        foreach ($stages as $stage) {
            $endTime = $startTime->copy()->addMinutes($stage['duration']);
            
            $this->productionStages()->create([
                'stage_name' => $stage['name'],
                'sequence' => $stage['sequence'],
                'planned_start_time' => $startTime,
                'planned_end_time' => $endTime,
                'planned_duration_minutes' => $stage['duration'],
                'requires_quality_check' => $stage['name'] === 'Quality Control',
            ]);
            
            $startTime = $endTime;
        }
    }

    protected function updateInventoryStock(): void
    {
        // Add finished goods to inventory
        $inventoryStock = InventoryStock::firstOrCreate([
            'product_id' => $this->product_id,
            'warehouse_id' => $this->warehouse_id,
        ]);

        $inventoryStock->increment('quantity', $this->actual_quantity);
    }

    public function generatePoNumber(): string
    {
        $prefix = 'PO';
        $date = now()->format('Ymd');
        $sequence = static::whereDate('created_at', today())->count() + 1;
        
        return sprintf('%s%s%04d', $prefix, $date, $sequence);
    }

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($order) {
            if (empty($order->po_number)) {
                $order->po_number = $order->generatePoNumber();
            }
        });
    }
}
