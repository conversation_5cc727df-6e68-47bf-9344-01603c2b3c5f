<?php

namespace App\Filament\Resources\SalesTransactionResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SaleItemsRelationManager extends RelationManager
{
    protected static string $relationship = 'saleItems';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('product_id')
                    ->label('Produk')
                    ->relationship('product', 'name')
                    ->searchable()
                    ->preload()
                    ->required()
                    ->reactive()
                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                        if ($state) {
                            $product = \App\Models\Product::find($state);
                            if ($product) {
                                $set('unit_price', $product->price);
                                $set('unit_cost', $product->cost_price);

                                // Get available stock for selected entitas
                                $salesTransaction = $this->getOwnerRecord();
                                if ($salesTransaction && $salesTransaction->entitas_id) {
                                    $availableStock = $product->getAvailableStockByEntitas($salesTransaction->entitas_id);
                                    $set('available_stock', $availableStock);
                                }
                            }
                        }
                    }),
                Forms\Components\TextInput::make('quantity')
                    ->label('Jumlah')
                    ->numeric()
                    ->required()
                    ->minValue(1)
                    ->reactive()
                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                        $unitPrice = $get('unit_price') ?: 0;
                        $unitCost = $get('unit_cost') ?: 0;
                        $quantity = $state ?: 0;

                        $set('total_price', $quantity * $unitPrice);
                        $set('total_cost', $quantity * $unitCost);
                    }),
                Forms\Components\TextInput::make('unit_price')
                    ->label('Harga Satuan')
                    ->numeric()
                    ->prefix('Rp')
                    ->required()
                    ->reactive()
                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                        $quantity = $get('quantity') ?: 0;
                        $unitCost = $get('unit_cost') ?: 0;
                        $set('total_price', $quantity * $state);
                        $set('total_cost', $quantity * $unitCost);
                    }),
                Forms\Components\TextInput::make('unit_cost')
                    ->label('Harga Pokok')
                    ->numeric()
                    ->prefix('Rp')
                    ->required()
                    ->reactive()
                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                        $quantity = $get('quantity') ?: 0;
                        $set('total_cost', $quantity * $state);
                    }),
                Forms\Components\TextInput::make('total_price')
                    ->label('Total Harga')
                    ->numeric()
                    ->prefix('Rp')
                    ->disabled(),
                Forms\Components\TextInput::make('total_cost')
                    ->label('Total HPP')
                    ->numeric()
                    ->prefix('Rp')
                    ->disabled(),
                Forms\Components\Placeholder::make('stock_info')
                    ->label('Stok Tersedia')
                    ->content(function (callable $get) {
                        $productId = $get('product_id');
                        if ($productId) {
                            $salesTransaction = $this->getOwnerRecord();
                            if ($salesTransaction && $salesTransaction->entitas_id) {
                                $product = \App\Models\Product::find($productId);
                                if ($product) {
                                    $availableStock = $product->getAvailableStockByEntitas($salesTransaction->entitas_id);
                                    return "Stok tersedia: {$availableStock} unit";
                                }
                            }
                        }
                        return 'Pilih produk untuk melihat stok';
                    }),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('product.nama')
            ->columns([
                Tables\Columns\TextColumn::make('product.nama')
                    ->label('Produk')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('quantity')
                    ->label('Jumlah')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('unit_price')
                    ->label('Harga Satuan')
                    ->money('IDR')
                    ->sortable(),
                Tables\Columns\TextColumn::make('total_price')
                    ->label('Total Harga')
                    ->money('IDR')
                    ->sortable(),
                Tables\Columns\TextColumn::make('unit_cost')
                    ->label('HPP Satuan')
                    ->money('IDR')
                    ->toggleable(),
                Tables\Columns\TextColumn::make('total_cost')
                    ->label('Total HPP')
                    ->money('IDR')
                    ->toggleable(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->before(function (array $data) {
                        // Validate stock before creating
                        $this->validateStock($data);
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->before(function (array $data) {
                        // Validate stock before editing
                        $this->validateStock($data);
                    }),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    /**
     * Validate stock availability
     */
    protected function validateStock(array $data): void
    {
        $salesTransaction = $this->getOwnerRecord();

        if (!$salesTransaction || !$salesTransaction->entitas_id) {
            throw new \Exception('Entitas harus dipilih terlebih dahulu');
        }

        $product = \App\Models\Product::find($data['product_id']);
        if (!$product) {
            throw new \Exception('Produk tidak ditemukan');
        }

        $availableStock = $product->getAvailableStockByEntitas($salesTransaction->entitas_id);
        $requestedQuantity = $data['quantity'];

        if ($requestedQuantity > $availableStock) {
            throw new \Exception("Stok tidak mencukupi. Stok tersedia: {$availableStock}, diminta: {$requestedQuantity}");
        }
    }
}
