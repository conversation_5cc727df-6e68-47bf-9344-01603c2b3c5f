<?php

namespace App\Filament\Manufacturing\Resources;

use App\Filament\Manufacturing\Resources\QualityInspectionResource\Pages;
use App\Filament\Manufacturing\Resources\QualityInspectionResource\RelationManagers;
use App\Models\QualityInspection;
use App\Models\ProductionOrder;
use App\Models\QualityControlPoint;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class QualityInspectionResource extends Resource
{
    protected static ?string $model = QualityInspection::class;

    protected static ?string $navigationIcon = 'heroicon-o-shield-check';

    protected static ?string $navigationGroup = 'Kontrol Kualitas';

    protected static ?string $navigationLabel = 'Inspeksi Kualitas';

    protected static ?string $modelLabel = 'Inspeksi Kualitas';

    protected static ?string $pluralModelLabel = 'Inspeksi Kualitas';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Inspection Information')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('inspection_number')
                                    ->label('Inspection Number')
                                    ->disabled()
                                    ->dehydrated(false),

                                Forms\Components\Select::make('overall_result')
                                    ->options([
                                        'pending' => 'Pending',
                                        'passed' => 'Passed',
                                        'failed' => 'Failed',
                                        'conditional_pass' => 'Conditional Pass',
                                    ])
                                    ->default('pending')
                                    ->required(),
                            ]),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Select::make('production_order_id')
                                    ->label('Production Order')
                                    ->options(ProductionOrder::with('product')->get()->mapWithKeys(function ($po) {
                                        return [$po->id => $po->po_number . ' - ' . $po->product->name];
                                    }))
                                    ->searchable()
                                    ->required(),

                                Forms\Components\Select::make('quality_control_point_id')
                                    ->label('Quality Control Point')
                                    ->options(QualityControlPoint::active()->pluck('name', 'id'))
                                    ->searchable()
                                    ->required(),
                            ]),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Select::make('inspector_id')
                                    ->label('Inspector')
                                    ->options(User::all()->pluck('name', 'id'))
                                    ->searchable()
                                    ->required()
                                    ->default(auth()->id()),

                                Forms\Components\DateTimePicker::make('inspection_date')
                                    ->required()
                                    ->default(now()),
                            ]),
                    ]),

                Forms\Components\Section::make('Quantity Results')
                    ->schema([
                        Forms\Components\Grid::make(4)
                            ->schema([
                                Forms\Components\TextInput::make('quantity_inspected')
                                    ->label('Quantity Inspected')
                                    ->numeric()
                                    ->required()
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                        // Auto-calculate remaining quantities
                                        $passed = $get('quantity_passed') ?? 0;
                                        $failed = $get('quantity_failed') ?? 0;
                                        $rework = $get('quantity_rework') ?? 0;

                                        $total = $passed + $failed + $rework;
                                        if ($total > $state) {
                                            // Reset if total exceeds inspected
                                            $set('quantity_passed', 0);
                                            $set('quantity_failed', 0);
                                            $set('quantity_rework', 0);
                                        }
                                    }),

                                Forms\Components\TextInput::make('quantity_passed')
                                    ->label('Quantity Passed')
                                    ->numeric()
                                    ->default(0)
                                    ->reactive(),

                                Forms\Components\TextInput::make('quantity_failed')
                                    ->label('Quantity Failed')
                                    ->numeric()
                                    ->default(0)
                                    ->reactive(),

                                Forms\Components\TextInput::make('quantity_rework')
                                    ->label('Quantity Rework')
                                    ->numeric()
                                    ->default(0)
                                    ->reactive(),
                            ]),
                    ]),

                Forms\Components\Section::make('Inspection Flags')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Toggle::make('requires_rework')
                                    ->label('Requires Rework')
                                    ->default(false),

                                Forms\Components\Toggle::make('is_final_inspection')
                                    ->label('Final Inspection')
                                    ->default(false),
                            ]),
                    ]),

                Forms\Components\Section::make('Notes & Actions')
                    ->schema([
                        Forms\Components\Textarea::make('notes')
                            ->rows(3)
                            ->columnSpanFull(),

                        Forms\Components\Textarea::make('corrective_actions')
                            ->label('Corrective Actions')
                            ->rows(3)
                            ->columnSpanFull(),
                    ])
                    ->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('inspection_number')
                    ->label('Inspection #')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('productionOrder.po_number')
                    ->label('Production Order')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('productionOrder.product.name')
                    ->label('Product')
                    ->searchable()
                    ->wrap(),

                Tables\Columns\TextColumn::make('qualityControlPoint.name')
                    ->label('QC Point')
                    ->searchable()
                    ->wrap(),

                Tables\Columns\TextColumn::make('quantity_inspected')
                    ->label('Inspected')
                    ->numeric()
                    ->sortable(),

                Tables\Columns\TextColumn::make('quantity_passed')
                    ->label('Passed')
                    ->numeric()
                    ->sortable(),

                Tables\Columns\TextColumn::make('quantity_failed')
                    ->label('Failed')
                    ->numeric()
                    ->sortable(),

                Tables\Columns\TextColumn::make('overall_result')
                    ->label('Result')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'pending' => 'gray',
                        'passed' => 'success',
                        'failed' => 'danger',
                        'conditional_pass' => 'warning',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('inspector.name')
                    ->label('Inspector')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('inspection_date')
                    ->label('Date')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\IconColumn::make('requires_rework')
                    ->label('Rework')
                    ->boolean(),

                Tables\Columns\IconColumn::make('is_final_inspection')
                    ->label('Final')
                    ->boolean(),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('overall_result')
                    ->options([
                        'pending' => 'Pending',
                        'passed' => 'Passed',
                        'failed' => 'Failed',
                        'conditional_pass' => 'Conditional Pass',
                    ]),

                Tables\Filters\SelectFilter::make('production_order_id')
                    ->label('Production Order')
                    ->options(ProductionOrder::with('product')->get()->mapWithKeys(function ($po) {
                        return [$po->id => $po->po_number . ' - ' . $po->product->name];
                    }))
                    ->searchable(),

                Tables\Filters\TernaryFilter::make('requires_rework')
                    ->label('Requires Rework'),

                Tables\Filters\TernaryFilter::make('is_final_inspection')
                    ->label('Final Inspection'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('inspection_date', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\QualityTestsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListQualityInspections::route('/'),
            'create' => Pages\CreateQualityInspection::route('/create'),
            'view' => Pages\ViewQualityInspection::route('/{record}'),
            'edit' => Pages\EditQualityInspection::route('/{record}/edit'),
        ];
    }
}
