<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\GoodsReceipt;
use App\Models\PurchaseOrder;
use App\Models\Supplier;
use App\Models\Product;
use App\Models\Entitas;
use App\Models\Warehouse;
use App\Services\PostingRuleEngine;
use Carbon\Carbon;

class TestPostingRules extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:posting-rules';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test posting rules engine with sample data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing Posting Rules Engine...');

        try {
            // 1. Create a sample Goods Receipt
            $goodsReceipt = $this->createSampleGoodsReceipt();
            $this->info("Created Goods Receipt: {$goodsReceipt->receipt_number}");

            // 2. Execute posting rules
            $postingEngine = new PostingRuleEngine();
            $journalEntries = $postingEngine->execute($goodsReceipt);

            if (empty($journalEntries)) {
                $this->warn('No journal entries were created. Check posting rules configuration.');
                return;
            }

            $this->info("Created " . count($journalEntries) . " journal entries:");

            foreach ($journalEntries as $journalEntry) {
                $this->line("- Journal: {$journalEntry->journal_number}");
                $this->line("  Date: {$journalEntry->formatted_journal_date}");
                $this->line("  Debit: {$journalEntry->formatted_total_debit}");
                $this->line("  Credit: {$journalEntry->formatted_total_credit}");
                $this->line("  Status: {$journalEntry->status}");
                $this->line("  Balanced: " . ($journalEntry->isBalanced() ? 'Yes' : 'No'));

                // Show details
                foreach ($journalEntry->journalEntries as $detail) {
                    $this->line("    - {$detail->account->kode_akun} {$detail->account->nama_akun}");
                    if ($detail->debit > 0) {
                        $this->line("      Debit: Rp " . number_format($detail->debit, 0, ',', '.'));
                    }
                    if ($detail->credit > 0) {
                        $this->line("      Credit: Rp " . number_format($detail->credit, 0, ',', '.'));
                    }
                }
                $this->line('');
            }

            $this->info('Posting Rules Engine test completed successfully!');
        } catch (\Exception $e) {
            $this->error("Test failed: " . $e->getMessage());
            $this->error("Stack trace: " . $e->getTraceAsString());
        }
    }

    private function createSampleGoodsReceipt()
    {
        // Get or create required data
        $supplier = Supplier::first();
        if (!$supplier) {
            $supplier = Supplier::create([
                'nama' => 'Test Supplier',
                'alamat' => 'Test Address',
                'telepon' => '123456789',
                'email' => '<EMAIL>',
                'created_by' => 1,
            ]);
        }

        $entitas = Entitas::first();
        if (!$entitas) {
            $this->error('No entitas found. Please create at least one entitas.');
            return null;
        }

        $warehouse = Warehouse::first();
        if (!$warehouse) {
            $warehouse = Warehouse::create([
                'name' => 'Test Warehouse',
                'code' => 'WH001',
                'address' => 'Test Warehouse Address',
                'phone' => '123456789',
                'manager_name' => 'Test Manager',
                'is_active' => true,
                'created_by' => 1,
            ]);
        }

        $product = Product::first();
        if (!$product) {
            $product = Product::create([
                'sku' => 'TEST001',
                'name' => 'Test Product',
                'description' => 'Test Product for Posting Rules',
                'cost_price' => 10000,
                'price' => 15000,
                'is_active' => true,
                'is_sellable' => true,
                'is_purchasable' => true,
                'primary_unit' => 'pcs',
                'stock_quantity' => 0,
            ]);
        }

        // Create Purchase Order first
        $purchaseOrder = PurchaseOrder::create([
            'po_number' => 'PO' . Carbon::now()->format('YmdHis'),
            'po_date' => Carbon::now(),
            'supplier_id' => $supplier->id,
            'entitas_id' => $entitas->id,
            'warehouse_id' => $warehouse->id,
            'status' => 'Approved',
            'subtotal' => 100000,
            'tax_amount' => 10000,
            'total_amount' => 110000,
            'notes' => 'Test PO for posting rules',
            'created_by' => 1,
        ]);

        // Create PO Item
        $purchaseOrder->purchaseOrderItems()->create([
            'product_id' => $product->id,
            'quantity_ordered' => 10,
            'unit_price' => 10000,
            'total_price' => 100000,
            'notes' => 'Test item',
        ]);

        // Create Goods Receipt
        $goodsReceipt = GoodsReceipt::create([
            'receipt_number' => 'GR' . Carbon::now()->format('YmdHis'),
            'receipt_date' => Carbon::now(),
            'purchase_order_id' => $purchaseOrder->id,
            'supplier_id' => $supplier->id,
            'warehouse_id' => $warehouse->id,
            'entitas_id' => $entitas->id,
            'status' => 'Completed',
            'total_quantity' => 10,
            'total_value' => 100000,
            'notes' => 'Test GR for posting rules',
            'received_by' => 1,
            'received_at' => Carbon::now(),
        ]);

        // Create GR Item
        $goodsReceipt->goodsReceiptItems()->create([
            'product_id' => $product->id,
            'purchase_order_item_id' => $purchaseOrder->purchaseOrderItems->first()->id,
            'quantity_ordered' => 10,
            'quantity_received' => 10,
            'unit_cost' => 10000,
            'total_cost' => 100000,
            'notes' => 'Test item received',
        ]);

        return $goodsReceipt;
    }
}
