<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('boms', function (Blueprint $table) {
            $table->id();
            $table->string('bom_number')->unique();
            $table->string('name');
            $table->text('description')->nullable();
            $table->foreignId('product_id')->constrained('products')->onDelete('cascade');
            $table->string('version', 10)->default('1.0');
            $table->enum('status', ['draft', 'active', 'inactive', 'archived'])->default('draft');
            $table->decimal('quantity', 10, 2)->default(1); // Base quantity for this BOM
            $table->string('unit', 20)->default('pcs');
            $table->decimal('total_material_cost', 15, 2)->default(0);
            $table->decimal('labor_cost_per_unit', 15, 2)->default(0);
            $table->decimal('overhead_cost_per_unit', 15, 2)->default(0);
            $table->decimal('total_cost_per_unit', 15, 2)->default(0);
            $table->integer('lead_time_days')->default(0);
            $table->text('notes')->nullable();
            $table->foreignId('created_by')->constrained('users');
            $table->foreignId('approved_by')->nullable()->constrained('users');
            $table->timestamp('approved_at')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['product_id', 'status']);
            $table->index(['status', 'created_at']);
            $table->index('bom_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('boms');
    }
};
