<?php

namespace App\Filament\Inventory\Pages;

use Filament\Pages\Dashboard as BaseDashboard;

class Dashboard extends BaseDashboard
{
    protected static ?string $navigationIcon = 'heroicon-o-home';

    protected static string $view = 'filament-panels::pages.dashboard';

    protected static ?string $title = 'Inventory Dashboard';

    protected static ?string $navigationGroup = 'Dashboard';

    protected static ?int $navigationSort = -2;

    public function getWidgets(): array
    {
        return [
            \App\Filament\Inventory\Widgets\TotalProductsWidget::class,
            \App\Filament\Inventory\Widgets\ActiveWarehousesWidget::class,
            \App\Filament\Inventory\Widgets\LowStockAlertsWidget::class,
            \App\Filament\Inventory\Widgets\PendingOrdersWidget::class,
            \App\Filament\Inventory\Widgets\StockMovementChart::class,
        ];
    }

    public function getColumns(): int | string | array
    {
        return [
            'md' => 2,
            'xl' => 4,
        ];
    }
}
