<?php

namespace App\Filament\Manufacturing\Resources\ProductionExpenseResource\Pages;

use App\Filament\Manufacturing\Resources\ProductionExpenseResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListProductionExpenses extends ListRecords
{
    protected static string $resource = ProductionExpenseResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
