<?php

namespace Database\Seeders;

use App\Models\Mitra;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class MitraVieraQuinosSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        include database_path('seeders/data/mitra_viera.php');

        $count = count($dataList);
        $bar = $this->command->getOutput()->createProgressBar($count);
        $bar->start();

        try {
            DB::transaction(function () use ($dataList, $bar) {
                foreach ($dataList as $data) {
                    if (!Mitra::where($data)->first()) {
                        $data['jenis_kerjasama'] = 'konsinyasi';
                        Mitra::create($data);
                        $bar->advance();
                    }
                }
            });

            $bar->finish();
            $this->command->newLine();
            $this->command->info("✅ Selesai insert {$count} data Mitra.");
        } catch (\Throwable $e) {
            $bar->clear();
            $this->command->error("❌ Gagal insert data: " . $e->getMessage());
        }
    }
}
