<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bom_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('bom_id')->constrained('boms')->onDelete('cascade');
            $table->foreignId('component_product_id')->constrained('products')->onDelete('cascade');
            $table->decimal('quantity_required', 10, 4); // Quantity needed per BOM unit
            $table->string('unit', 20);
            $table->decimal('unit_cost', 15, 2)->default(0);
            $table->decimal('total_cost', 15, 2)->default(0);
            $table->decimal('waste_percentage', 5, 2)->default(0); // Expected waste %
            $table->decimal('actual_quantity_required', 10, 4)->nullable(); // Including waste
            $table->boolean('is_critical')->default(false); // Critical component
            $table->text('notes')->nullable();
            $table->integer('sequence')->default(0); // Order in production process
            $table->timestamps();

            // Indexes
            $table->index(['bom_id', 'sequence']);
            $table->index('component_product_id');
            $table->unique(['bom_id', 'component_product_id'], 'bom_component_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bom_items');
    }
};
