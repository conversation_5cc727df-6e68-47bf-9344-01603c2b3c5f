<?php

namespace App\Filament\Accounting\Resources\TransactionCategoryResource\Pages;

use App\Filament\Accounting\Resources\TransactionCategoryResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewTransactionCategory extends ViewRecord
{
    protected static string $resource = TransactionCategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->label('Edit Kategori')
                ->icon('heroicon-o-pencil'),
            Actions\DeleteAction::make()
                ->label('Hapus Kategori')
                ->icon('heroicon-o-trash'),
        ];
    }
}
