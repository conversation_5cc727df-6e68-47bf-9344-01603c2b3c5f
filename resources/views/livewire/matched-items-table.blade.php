<div class="space-y-4">
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Bank Statement
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Buku Kas
                    </th>
                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Selisih
                    </th>
                    <th class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Aksi
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                @forelse($matchedPairs as $pair)
                    @php
                        $statementItem = $pair->where('type', 'statement')->first();
                        $bookItem = $pair->where('type', 'book')->first();
                        $difference = $statementItem && $bookItem ? abs($statementItem->amount - $bookItem->amount) : 0;
                    @endphp
                    <tr class="bg-green-50 hover:bg-green-100">
                        <td class="px-6 py-4">
                            @if ($statementItem)
                                <div class="text-sm text-gray-900">
                                    <div class="font-medium">{{ $statementItem->description }}</div>
                                    <div class="text-xs text-gray-500">
                                        {{ $statementItem->transaction_date->format('d/m/Y') }}
                                    </div>
                                    <div class="text-sm font-medium text-green-600">
                                        Rp {{ number_format(abs($statementItem->amount), 2, ',', '.') }}
                                    </div>
                                </div>
                            @endif
                        </td>
                        <td class="px-6 py-4">
                            @if ($bookItem)
                                <div class="text-sm text-gray-900">
                                    <div class="font-medium">{{ $bookItem->description }}</div>
                                    <div class="text-xs text-gray-500">
                                        {{ $bookItem->transaction_date->format('d/m/Y') }}
                                    </div>
                                    <div class="text-sm font-medium text-green-600">
                                        Rp {{ number_format(abs($bookItem->amount), 2, ',', '.') }}
                                    </div>
                                </div>
                            @endif
                        </td>
                        <td class="px-6 py-4 text-center">
                            @if ($difference > 0.01)
                                <span
                                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    Rp {{ number_format($difference, 2, ',', '.') }}
                                </span>
                            @else
                                <span
                                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Exact Match
                                </span>
                            @endif
                        </td>
                        <td class="px-6 py-4 text-center">
                            <button wire:click="unmatchPair({{ $statementItem?->id ?? $bookItem?->id }})"
                                class="text-red-600 hover:text-red-900 text-xs bg-red-100 hover:bg-red-200 px-3 py-1 rounded">
                                Batalkan Match
                            </button>
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="4" class="px-6 py-8 text-center text-gray-500">
                            Belum ada item yang dicocokkan. Gunakan "Auto Match" atau cocokkan secara manual.
                        </td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>
</div>
