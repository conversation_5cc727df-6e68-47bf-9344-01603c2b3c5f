<?php

namespace App\Filament\Accounting\Resources;

use App\Filament\Accounting\Resources\PaymentMethodResource\Pages;
use App\Models\PaymentMethod;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Table;

// --- ADDED: Importing all components for cleaner code ---
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Actions\EditAction;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;

class PaymentMethodResource extends Resource
{
    protected static ?string $model = PaymentMethod::class;

    protected static ?string $navigationIcon = 'heroicon-o-banknotes';
    protected static ?string $navigationGroup = 'Settings';
    protected static ?string $navigationLabel = 'Metode Pembayaran';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Detail Metode Pembayaran')
                    ->schema([
                        Select::make('method_name')
                            ->label('Tipe Metode Pembayaran')
                            ->options(PaymentMethod::METHOD_NAME_LABELS)
                            ->required()
                            ->searchable()
                            ->live()
                            ->columnSpanFull()
                            ->helperText('Pilih tipe metode pembayaran yang akan digunakan.'),

                        TextInput::make('method_display_name')
                            ->label('Nama Tampilan')
                            ->required()
                            ->maxLength(255)
                            ->unique(PaymentMethod::class, 'method_display_name', ignoreRecord: true)
                            ->placeholder('Contoh: BNI Kantor Pusat, Kas Kecil Operasional')
                            ->helperText('Nama yang akan ditampilkan untuk membedakan metode pembayaran yang sama. Harus unik.')
                            ->columnSpanFull(),

                        Select::make('akun_id')
                            ->label('Chart of Account (COA)')
                            ->relationship('akun', 'nama_akun')
                            ->searchable()
                            ->preload()
                            ->required()
                            ->getOptionLabelFromRecordUsing(fn($record) => "{$record->kode_akun} - {$record->nama_akun}")
                            ->helperText('Pilih akun COA yang akan digunakan untuk jurnal otomatis.')
                            ->columnSpanFull(),

                        TextInput::make('bank_name')
                            ->label('Nama Bank/Institusi')
                            ->maxLength(255)
                            ->visible(fn(\Filament\Forms\Get $get) => in_array($get('method_name'), ['bank_transfer', 'virtual_account', 'credit_card', 'debit_card']))
                            ->required(fn(\Filament\Forms\Get $get) => in_array($get('method_name'), ['bank_transfer', 'virtual_account']))
                            ->placeholder('Contoh: Bank BNI, Bank Mandiri'),

                        TextInput::make('account_number')
                            ->label('Nomor Rekening/Akun')
                            ->maxLength(255)
                            ->visible(fn(\Filament\Forms\Get $get) => in_array($get('method_name'), ['bank_transfer', 'virtual_account', 'credit_card', 'debit_card', 'e_wallet']))
                            ->required(fn(\Filament\Forms\Get $get) => in_array($get('method_name'), ['bank_transfer', 'virtual_account']))
                            ->placeholder('Nomor rekening atau ID akun'),

                        TextInput::make('account_name')
                            ->label('Atas Nama')
                            ->maxLength(255)
                            ->visible(fn(\Filament\Forms\Get $get) => in_array($get('method_name'), ['bank_transfer', 'virtual_account', 'credit_card', 'debit_card', 'e_wallet', 'check', 'giro']))
                            ->required(fn(\Filament\Forms\Get $get) => in_array($get('method_name'), ['bank_transfer', 'virtual_account', 'check', 'giro']))
                            ->placeholder('Nama pemilik rekening/akun'),

                        Toggle::make('is_active')
                            ->label('Aktif')
                            ->default(true)
                            ->required(),

                        Textarea::make('notes')
                            ->label('Catatan Tambahan')
                            ->rows(3)
                            ->columnSpanFull()
                            ->placeholder('Informasi tambahan seperti cabang bank, syarat penggunaan, dll.'),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('method_display_name')
                    ->label('Nama Metode')
                    ->searchable()
                    ->weight('bold')
                    ->sortable(),

                TextColumn::make('method_name')
                    ->label('Tipe')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'bank_transfer' => 'success',
                        'cash' => 'warning',
                        'check', 'giro' => 'info',
                        'credit_card', 'debit_card' => 'primary',
                        'e_wallet' => 'purple',
                        'virtual_account' => 'indigo',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => PaymentMethod::METHOD_NAME_LABELS[$state] ?? $state),

                TextColumn::make('akun.kode_akun')
                    ->label('Kode COA')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('akun.nama_akun')
                    ->label('Nama Akun COA')
                    ->searchable()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('bank_name')
                    ->label('Bank/Institusi')
                    ->searchable()
                    ->placeholder('N/A')
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('account_number')
                    ->label('Nomor Rekening')
                    ->searchable()
                    ->copyable()
                    ->placeholder('N/A')
                    ->toggleable(isToggledHiddenByDefault: true),

                TextColumn::make('account_name')
                    ->label('Atas Nama')
                    ->searchable()
                    ->placeholder('N/A')
                    ->toggleable(isToggledHiddenByDefault: true),

                ToggleColumn::make('is_active')
                    ->label('Aktif'),
            ])
            ->filters([
                SelectFilter::make('method_name')
                    ->label('Tipe Metode')
                    ->options(PaymentMethod::METHOD_NAME_LABELS)
                    ->multiple(),

                TernaryFilter::make('is_active')
                    ->label('Status')
                    ->placeholder('Semua')
                    ->trueLabel('Aktif')
                    ->falseLabel('Tidak Aktif'),

                Filter::make('has_bank_details')
                    ->label('Memiliki Detail Bank')
                    ->query(fn($query) => $query->whereNotNull('bank_name'))
                    ->toggle(),
            ])
            ->actions([
                ViewAction::make(),
                EditAction::make(),
            ])
            ->bulkActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPaymentMethods::route('/'),
            'create' => Pages\CreatePaymentMethod::route('/create'),
            'edit' => Pages\EditPaymentMethod::route('/{record}/edit'),
            'view' => Pages\ViewPaymentMethod::route('/{record}'),
        ];
    }
}
