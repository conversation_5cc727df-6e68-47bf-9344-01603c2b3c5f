<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('expense_request_approvals', function (Blueprint $table) {
            $table->id();

            $table->foreignId('expense_request_id')->constrained('expense_requests')->onDelete('cascade');

            $table->foreignId('action_by')->constrained('users')->onDelete('cascade');
            $table->timestamp('action_at');

            $table->enum('status', ['Draft', 'Submitted', 'Approved', 'Rejected', 'Paid', 'Cancelled'])->default('Draft');
            $table->text('note')->nullable();

            $table->integer('step')->default(0);

            $table->timestamps();
        });

        Schema::table('expense_requests', function (Blueprint $table) {
            $table->dropColumn([
                'status',
                'total_amount',
                'approved_by',
                'approved_at',
                'rejected_by',
                'rejected_at',
                'rejection_reason',
                'approval_data',
            ]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('expense_request_approvals');
        Schema::table('expense_requests', function (Blueprint $table) {
            $table->enum('status', ['Draft', 'Submitted', 'Approved', 'Rejected', 'Paid', 'Cancelled'])->default('Draft');
            $table->decimal('total_amount', 12, 2);
            $table->unsignedBigInteger('approved_by')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->unsignedBigInteger('rejected_by')->nullable();
            $table->timestamp('rejected_at')->nullable();
            $table->text('rejection_reason')->nullable();
            $table->json('approval_data')->nullable()->after('rejection_reason');
        });
    }
};
