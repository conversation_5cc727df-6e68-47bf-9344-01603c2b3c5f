<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class StockOpnameItem extends Model
{
    use HasFactory;

    protected $table = 'stock_opname_items';

    protected $fillable = [
        'stock_opname_id',
        'product_id',
        'system_quantity',
        'physical_quantity',
        'variance_quantity',
        'unit_cost',
        'variance_value',
        'variance_type',
        'variance_reason',
        'notes',
        'is_counted',
        'counted_at',
        'counted_by',
    ];

    protected $dates = ['counted_at'];

    protected $casts = [
        'counted_at' => 'datetime',
        'system_quantity' => 'integer',
        'physical_quantity' => 'integer',
        'variance_quantity' => 'integer',
        'unit_cost' => 'decimal:2',
        'variance_value' => 'decimal:2',
        'is_counted' => 'boolean',
    ];

    // Relationships
    public function stockOpname()
    {
        return $this->belongsTo(StockOpname::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function countedBy()
    {
        return $this->belongsTo(User::class, 'counted_by');
    }

    // Helper methods
    public function getVariancePercentageAttribute()
    {
        if ($this->system_quantity == 0) {
            return $this->physical_quantity > 0 ? 100 : 0;
        }

        return round(($this->variance_quantity / $this->system_quantity) * 100, 2);
    }

    public function getVarianceColorAttribute()
    {
        return match ($this->variance_type) {
            'Surplus' => 'success',
            'Shortage' => 'danger',
            'Match' => 'gray',
            default => 'gray'
        };
    }

    public function getFormattedVarianceValueAttribute()
    {
        return 'Rp ' . number_format($this->variance_value, 0, ',', '.');
    }

    public function getFormattedUnitCostAttribute()
    {
        return 'Rp ' . number_format($this->unit_cost, 0, ',', '.');
    }

    public function markAsCounted($physicalQuantity, $userId = null, $notes = null)
    {
        $this->physical_quantity = $physicalQuantity;
        $this->variance_quantity = $physicalQuantity - $this->system_quantity;
        $this->variance_value = $this->variance_quantity * $this->unit_cost;

        // Determine variance type
        if ($this->variance_quantity > 0) {
            $this->variance_type = 'Surplus';
        } elseif ($this->variance_quantity < 0) {
            $this->variance_type = 'Shortage';
        } else {
            $this->variance_type = 'Match';
        }

        $this->is_counted = true;
        $this->counted_at = Carbon::now();
        $this->counted_by = $userId ?: auth()->id();
        $this->notes = $notes;

        $this->save();
    }

    public function resetCount()
    {
        $this->physical_quantity = null;
        $this->variance_quantity = 0;
        $this->variance_value = 0;
        $this->variance_type = 'Match';
        $this->is_counted = false;
        $this->counted_at = null;
        $this->counted_by = null;
        $this->notes = null;

        $this->save();
    }

    // Auto-calculate fields
    protected static function boot()
    {
        parent::boot();

        static::saving(function ($item) {
            if (!is_null($item->physical_quantity)) {
                // Calculate variance
                $item->variance_quantity = $item->physical_quantity - $item->system_quantity;
                $item->variance_value = $item->variance_quantity * $item->unit_cost;

                // Determine variance type
                if ($item->variance_quantity > 0) {
                    $item->variance_type = 'Surplus';
                } elseif ($item->variance_quantity < 0) {
                    $item->variance_type = 'Shortage';
                } else {
                    $item->variance_type = 'Match';
                }
            }
        });
    }
}
