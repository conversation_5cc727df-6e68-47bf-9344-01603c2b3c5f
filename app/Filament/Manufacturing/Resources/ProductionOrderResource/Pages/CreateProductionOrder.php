<?php

namespace App\Filament\Manufacturing\Resources\ProductionOrderResource\Pages;

use App\Filament\Manufacturing\Resources\ProductionOrderResource;
use Filament\Resources\Pages\CreateRecord;

class CreateProductionOrder extends CreateRecord
{
    protected static string $resource = ProductionOrderResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('view', ['record' => $this->getRecord()]);
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Auto-calculate total costs
        $data['total_planned_cost'] = ($data['planned_material_cost'] ?? 0) + 
                                     ($data['planned_labor_cost'] ?? 0) + 
                                     ($data['planned_overhead_cost'] ?? 0);
        
        $data['total_actual_cost'] = ($data['actual_material_cost'] ?? 0) + 
                                    ($data['actual_labor_cost'] ?? 0) + 
                                    ($data['actual_overhead_cost'] ?? 0);

        return $data;
    }

    protected function afterCreate(): void
    {
        // Generate material requirements when production order is created
        if ($this->record->status === 'released') {
            $this->record->generateMaterialRequirements();
            $this->record->generateProductionStages();
        }
    }
}
