<?php

namespace App\Filament\Resources;

use App\Filament\Resources\KaryawanPermissionTypeResource\Pages;
use App\Filament\Resources\KaryawanPermissionTypeResource\RelationManagers;
use App\Models\KaryawanPermissionType;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class KaryawanPermissionTypeResource extends Resource
{
    protected static ?string $model = KaryawanPermissionType::class;

    protected static ?string $navigationIcon = 'heroicon-o-adjustments-vertical';
    protected static ?string $navigationLabel = 'Permission Type';
    protected static ?string $navigationGroup = 'HR Management';
    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Tipe Izin Karyawan')
                    ->description('Buat tipe izin karyawan.')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Nama Tipe')
                            ->placeholder('Contoh: notifikasi_sesuatu')
                            ->required()
                            ->helperText('Nama tipe izin.'),

                        Forms\Components\TextInput::make('description')
                            ->label('Deskripsi')
                            ->placeholder('Contoh: Untuk izin sesuatu'),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Nama Tipe')
                    ->searchable()
                    ->sortable()
                    ->copyable()
                    ->weight('bold')
                    ->badge(),

                Tables\Columns\TextColumn::make('description')
                    ->label('Deskripsi')
                    ->searchable()
                    ->limit(50),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListKaryawanPermissionTypes::route('/'),
            'create' => Pages\CreateKaryawanPermissionType::route('/create'),
            'edit' => Pages\EditKaryawanPermissionType::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
}
