<?php

namespace App\Filament\Setting\Resources;

use App\Filament\Setting\Resources\NotificationSettingResource\Pages;
use App\Models\NotificationSetting;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class NotificationSettingResource extends Resource
{
    protected static ?string $model = NotificationSetting::class;

    protected static ?string $navigationIcon = 'heroicon-o-bell-alert'; // <-- Icon diubah agar lebih sesuai

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                // Menggunakan Section untuk tampilan yang lebih rapi
                Forms\Components\Section::make('Aturan Notifikasi')
                    ->description('Tentukan event apa yang akan mengirim notifikasi ke karyawan mana.')
                    ->schema([
                        // Dropdown untuk memilih event. Mencegah typo.
                        Forms\Components\Select::make('event_id')
                            ->label('Event')
                            ->relationship('event', 'event_name')
                            ->searchable()
                            ->preload()
                            ->required()
                            ->helperText('Pilih event yang akan memicu notifikasi.'),

                        // Dropdown untuk memilih karyawan. Searchable agar mudah dicari.
                        Forms\Components\Select::make('user_id')
                            ->label('Karyawan Penerima Notifikasi')
                            ->relationship('karyawan', 'nama_lengkap') // Mengambil 'nama_lengkap' dari relasi 'karyawan'
                            ->searchable()
                            ->preload()
                            ->required()
                            ->helperText('Pilih karyawan yang akan menerima pesan.'),

                        // Opsi channel, jika nanti ingin menambah email, dll.
                        Forms\Components\Select::make('channel')
                            ->label('Kirim Melalui')
                            ->options([
                                'whatsapp' => 'WhatsApp',
                                'email' => 'Email',
                                'database' => 'Database (Notifikasi di aplikasi)',
                            ])
                            ->default('whatsapp')
                            ->required(),

                        // Toggle untuk mengaktifkan/menonaktifkan aturan
                        Forms\Components\Toggle::make('is_active')
                            ->label('Status Aturan Aktif')
                            ->default(true)
                            ->required(),
                    ])->columns(2), // Membuat form menjadi 2 kolom
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                // Menampilkan nama event dengan badge agar lebih menarik
                Tables\Columns\TextColumn::make('event.event_name')
                    ->label('Event')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'penjualan_baru' => 'info',
                        'penjualan_disetujui' => 'success',
                        'penjualan_ditolak' => 'danger',
                        'penjualan_membutuhkan_revisi' => 'warning',
                        default => 'gray',
                    })
                    ->searchable(),

                // Menampilkan nama karyawan dari relasi
                Tables\Columns\TextColumn::make('karyawan.nama_lengkap')
                    ->label('Karyawan Penerima')
                    ->searchable()
                    ->sortable(),

                // Menampilkan channel
                Tables\Columns\TextColumn::make('channel')
                    ->label('Channel')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'whatsapp' => 'success',
                        default => 'gray',
                    })
                    ->sortable(),

                // Menampilkan status aktif dengan toggle yang bisa di-klik langsung
                Tables\Columns\ToggleColumn::make('is_active')
                    ->label('Aktif'),

                // Menampilkan tanggal dibuat
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat Pada')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true), // Sembunyikan default
            ])
            ->filters([
                // Filter berdasarkan nama event
                Tables\Filters\SelectFilter::make('event.event_name')
                    ->label('Event')
                    ->relationship('event', 'event_name')
                    ->searchable()
                    ->preload(),

                // Filter berdasarkan karyawan
                Tables\Filters\SelectFilter::make('user_id')
                    ->label('User')
                    ->relationship('karyawan', 'nama_lengkap')
                    ->searchable()
                    ->preload(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListNotificationSettings::route('/'),
            'create' => Pages\CreateNotificationSetting::route('/create'),
            'edit' => Pages\EditNotificationSetting::route('/{record}/edit'),
            'view' => Pages\ViewNotificationSetting::route('/{record}'), // <-- Tambahkan halaman view
        ];
    }
}
