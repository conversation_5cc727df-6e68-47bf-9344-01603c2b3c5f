<?php

namespace App\Filament\Resources\KaryawanPermissionTypeResource\Pages;

use App\Filament\Resources\KaryawanPermissionTypeResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateKaryawanPermissionType extends CreateRecord
{
    protected static string $resource = KaryawanPermissionTypeResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
