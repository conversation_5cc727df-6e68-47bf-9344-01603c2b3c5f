<?php

namespace App\Filament\Warehouse\Pages;

use App\Models\StockMovement;
use App\Models\Product;
use App\Models\Category;
use App\Models\Warehouse;
use Filament\Forms;
use Filament\Pages\Page;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Illuminate\Database\Eloquent\Builder;
use Filament\Actions;
use Carbon\Carbon;

class MovementHistoryReport extends Page implements HasTable
{
    use InteractsWithTable;

    protected static ?string $navigationIcon = 'heroicon-o-clock';

    protected static string $view = 'filament.warehouse.pages.movement-history-report';

    protected static ?string $title = 'Movement History Report';

    protected static ?string $navigationGroup = 'Reports';

    protected static ?int $navigationSort = 2;

    public $filters = [
        'warehouse_id' => null,
        'product_id' => null,
        'movement_type' => null,
        'date_from' => null,
        'date_to' => null,
    ];

    public function mount(): void
    {
        $this->filters['date_from'] = now()->startOfMonth()->format('Y-m-d');
        $this->filters['date_to'] = now()->format('Y-m-d');
    }

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('export')
                ->label('Export CSV')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('success')
                ->action(function () {
                    return $this->exportToCsv();
                }),
        ];
    }

    public function table(Table $table): Table
    {
        return $table
            ->query($this->getTableQuery())
            ->columns([
                Tables\Columns\TextColumn::make('movement_number')
                    ->label('Movement #')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('movement_date')
                    ->label('Date')
                    ->date('d/m/Y')
                    ->sortable(),
                Tables\Columns\BadgeColumn::make('movement_type')
                    ->label('Type')
                    ->colors([
                        'success' => ['Purchase_Receipt', 'Transfer_In', 'Adjustment_In', 'Production_In', 'Opening_Balance'],
                        'danger' => ['Sales_Issue', 'Transfer_Out', 'Adjustment_Out', 'Production_Out'],
                    ]),
                Tables\Columns\TextColumn::make('product.kode')
                    ->label('Product Code')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('product.nama')
                    ->label('Product Name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('warehouse.name')
                    ->label('Warehouse')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('quantity')
                    ->label('Quantity')
                    ->badge()
                    ->color(function ($state) {
                        return $state > 0 ? 'success' : 'danger';
                    })
                    ->formatStateUsing(function ($state) {
                        return ($state > 0 ? '+' : '') . number_format($state);
                    }),
                Tables\Columns\TextColumn::make('unit_cost')
                    ->label('Unit Cost')
                    ->money('IDR'),
                Tables\Columns\TextColumn::make('total_value')
                    ->label('Total Value')
                    ->money('IDR')
                    ->color(function ($state) {
                        return $state > 0 ? 'success' : 'danger';
                    }),
                Tables\Columns\TextColumn::make('reference_number')
                    ->label('Reference')
                    ->searchable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('notes')
                    ->label('Notes')
                    ->limit(30)
                    ->tooltip(function (Tables\Columns\TextColumn $column): ?string {
                        $state = $column->getState();
                        if (strlen($state) <= 30) {
                            return null;
                        }
                        return $state;
                    })
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('movement_type')
                    ->label('Movement Type')
                    ->options([
                        'Opening_Balance' => 'Opening Balance',
                        'Purchase_Receipt' => 'Purchase Receipt',
                        'Sales_Issue' => 'Sales Issue',
                        'Transfer_In' => 'Transfer In',
                        'Transfer_Out' => 'Transfer Out',
                        'Adjustment_In' => 'Adjustment In',
                        'Adjustment_Out' => 'Adjustment Out',
                        'Production_In' => 'Production In',
                        'Production_Out' => 'Production Out',
                    ]),
                Tables\Filters\SelectFilter::make('warehouse_id')
                    ->label('Warehouse')
                    ->options(Warehouse::where('is_active', true)->pluck('name', 'id'))
                    ->searchable()
                    ->preload(),
                Tables\Filters\SelectFilter::make('product_id')
                    ->label('Product')
                    ->options(Product::pluck('name', 'id'))
                    ->searchable()
                    ->preload(),
                Tables\Filters\Filter::make('movement_date')
                    ->form([
                        Forms\Components\DatePicker::make('from')
                            ->label('From Date'),
                        Forms\Components\DatePicker::make('until')
                            ->label('Until Date'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('movement_date', '>=', $date),
                            )
                            ->when(
                                $data['until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('movement_date', '<=', $date),
                            );
                    }),
            ])
            ->headerActions([
                Tables\Actions\Action::make('filter')
                    ->label('Advanced Filters')
                    ->icon('heroicon-o-funnel')
                    ->form([
                        Forms\Components\DatePicker::make('date_from')
                            ->label('From Date')
                            ->default($this->filters['date_from']),
                        Forms\Components\DatePicker::make('date_to')
                            ->label('To Date')
                            ->default($this->filters['date_to']),
                        Forms\Components\Select::make('warehouse_id')
                            ->label('Warehouse')
                            ->options(Warehouse::where('is_active', true)->pluck('name', 'id'))
                            ->searchable()
                            ->preload(),
                        Forms\Components\Select::make('product_id')
                            ->label('Product')
                            ->options(Product::pluck('name', 'id'))
                            ->searchable()
                            ->preload(),
                        Forms\Components\Select::make('movement_type')
                            ->label('Movement Type')
                            ->options([
                                'Opening_Balance' => 'Opening Balance',
                                'Purchase_Receipt' => 'Purchase Receipt',
                                'Sales_Issue' => 'Sales Issue',
                                'Transfer_In' => 'Transfer In',
                                'Transfer_Out' => 'Transfer Out',
                                'Adjustment_In' => 'Adjustment In',
                                'Adjustment_Out' => 'Adjustment Out',
                                'Production_In' => 'Production In',
                                'Production_Out' => 'Production Out',
                            ]),
                    ])
                    ->action(function (array $data) {
                        $this->filters = array_merge($this->filters, $data);
                        $this->resetTable();
                    }),
            ])
            ->defaultSort('movement_date', 'desc')
            ->striped()
            ->paginated([25, 50, 100]);
    }

    protected function getTableQuery(): Builder
    {
        $query = StockMovement::query()
            ->with(['product.kategori', 'warehouse', 'entitas', 'createdBy']);

        // Apply date range filter
        if ($this->filters['date_from']) {
            $query->whereDate('movement_date', '>=', $this->filters['date_from']);
        }

        if ($this->filters['date_to']) {
            $query->whereDate('movement_date', '<=', $this->filters['date_to']);
        }

        // Apply other filters
        if ($this->filters['warehouse_id']) {
            $query->where('warehouse_id', $this->filters['warehouse_id']);
        }

        if ($this->filters['product_id']) {
            $query->where('product_id', $this->filters['product_id']);
        }

        if ($this->filters['movement_type']) {
            $query->where('movement_type', $this->filters['movement_type']);
        }

        return $query;
    }

    protected function exportToCsv()
    {
        $data = $this->getTableQuery()->get();

        $filename = 'movement_history_report_' . now()->format('Y_m_d_H_i_s') . '.csv';
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function () use ($data) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'Movement Number',
                'Date',
                'Type',
                'Product Code',
                'Product Name',
                'Warehouse',
                'Quantity',
                'Unit Cost',
                'Total Value',
                'Reference',
                'Notes',
                'Created By',
                'Created At'
            ]);

            // CSV data
            foreach ($data as $record) {
                fputcsv($file, [
                    $record->movement_number,
                    $record->movement_date->format('d/m/Y'),
                    $record->movement_type,
                    $record->product->kode,
                    $record->product->nama,
                    $record->warehouse->name,
                    $record->quantity,
                    $record->unit_cost,
                    $record->total_value,
                    $record->reference_number,
                    $record->notes,
                    $record->createdBy->name ?? '',
                    $record->created_at->format('d/m/Y H:i'),
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    public function getSummaryData()
    {
        $query = $this->getTableQuery();

        return [
            'total_movements' => $query->count(),
            'total_incoming' => $query->whereIn('movement_type', ['Purchase_Receipt', 'Transfer_In', 'Adjustment_In', 'Production_In', 'Opening_Balance'])->sum('quantity'),
            'total_outgoing' => abs($query->whereIn('movement_type', ['Sales_Issue', 'Transfer_Out', 'Adjustment_Out', 'Production_Out'])->sum('quantity')),
            'total_value_in' => $query->whereIn('movement_type', ['Purchase_Receipt', 'Transfer_In', 'Adjustment_In', 'Production_In', 'Opening_Balance'])->sum('total_value'),
            'total_value_out' => abs($query->whereIn('movement_type', ['Sales_Issue', 'Transfer_Out', 'Adjustment_Out', 'Production_Out'])->sum('total_value')),
        ];
    }
}
