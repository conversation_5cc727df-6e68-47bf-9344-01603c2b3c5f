<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('process_approval_flow_steps', function (Blueprint $table) {
            // Change role_id from integer to string to support both built-in and Shield roles
            $table->string('role_id', 50)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('process_approval_flow_steps', function (Blueprint $table) {
            // Revert back to integer (this might cause data loss if string values exist)
            $table->unsignedBigInteger('role_id')->change();
        });
    }
};
