<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Reconciliation Summary -->
        <div class="bg-white rounded-lg shadow p-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div class="text-center">
                    <div class="text-2xl font-bold text-blue-600">{{ $record->account->nama_akun }}</div>
                    <div class="text-sm text-gray-500">Akun Bank</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">Rp {{ number_format($record->opening_balance, 2, ',', '.') }}</div>
                    <div class="text-sm text-gray-500">Saldo Awal</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-purple-600">Rp {{ number_format($record->closing_balance, 2, ',', '.') }}</div>
                    <div class="text-sm text-gray-500"><PERSON><PERSON></div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold {{ $record->difference >= 0 ? 'text-green-600' : 'text-red-600' }}">
                        Rp {{ number_format(abs($record->difference), 2, ',', '.') }}
                    </div>
                    <div class="text-sm text-gray-500">{{ $record->difference >= 0 ? 'Surplus' : 'Defisit' }}</div>
                </div>
            </div>
        </div>

        <!-- Reconciliation Items -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Bank Statement Items -->
            <div class="bg-white rounded-lg shadow">
                <div class="p-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Bank Statement Items</h3>
                    <p class="text-sm text-gray-500">Items dari bank statement</p>
                </div>
                <div class="p-4">
                    @livewire('bank-reconciliation-items-table', [
                        'reconciliation' => $record,
                        'type' => 'statement'
                    ])
                </div>
            </div>

            <!-- Book Items -->
            <div class="bg-white rounded-lg shadow">
                <div class="p-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Book Items</h3>
                    <p class="text-sm text-gray-500">Items dari buku kas</p>
                </div>
                <div class="p-4">
                    @livewire('bank-reconciliation-items-table', [
                        'reconciliation' => $record,
                        'type' => 'book'
                    ])
                </div>
            </div>
        </div>

        <!-- Matched Items -->
        <div class="bg-white rounded-lg shadow">
            <div class="p-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Matched Items</h3>
                <p class="text-sm text-gray-500">Items yang sudah dicocokkan</p>
            </div>
            <div class="p-4">
                @livewire('matched-items-table', ['reconciliation' => $record])
            </div>
        </div>

        <!-- Instructions -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 class="text-sm font-semibold text-blue-900 mb-2">Petunjuk Rekonsiliasi:</h4>
            <ul class="text-sm text-blue-800 space-y-1">
                <li>1. Klik "Muat Entri Buku" untuk memuat transaksi dari jurnal</li>
                <li>2. Tambahkan item bank statement secara manual atau import</li>
                <li>3. Gunakan "Auto Match" untuk mencocokkan item secara otomatis</li>
                <li>4. Cocokkan item yang tersisa secara manual</li>
                <li>5. Klik "Selesaikan Rekonsiliasi" jika semua item sudah cocok</li>
            </ul>
        </div>
    </div>
</x-filament-panels::page>
