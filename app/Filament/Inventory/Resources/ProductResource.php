<?php

namespace App\Filament\Inventory\Resources;

use App\Filament\Inventory\Resources\ProductResource\Pages;
use App\Models\Product;
use App\Models\Category;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ProductResource extends Resource
{
    protected static ?string $model = Product::class;

    protected static ?string $navigationIcon = 'heroicon-o-cube';

    protected static ?string $navigationGroup = 'Master Data';

    protected static ?int $navigationSort = 1;

    protected static ?string $navigationLabel = 'Products';

    protected static ?string $modelLabel = 'Product';

    protected static ?string $pluralModelLabel = 'Products';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Product Information')
                    ->schema([
                        Forms\Components\TextInput::make('sku')
                            ->label('SKU')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('Example: PRD-001')
                            ->unique(ignoreRecord: true),
                        Forms\Components\TextInput::make('name')
                            ->label('Product Name')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('Product name'),
                        Forms\Components\Select::make('category_id')
                            ->label('Category')
                            ->options(Category::pluck('name', 'id'))
                            ->searchable()
                            ->preload()
                            ->required(),
                        Forms\Components\Select::make('product_type')
                            ->label('Product Type')
                            ->options([
                                'finished_goods' => 'Barang Jadi',
                                'raw_material' => 'Bahan Baku',
                                'work_in_progress' => 'Work in Progress',
                                'service' => 'Layanan/Jasa',
                                'consignment' => 'Konsinyasi',
                                'bundle' => 'Paket/Bundle',
                                'digital' => 'Produk Digital',
                                'spare_part' => 'Suku Cadang',
                            ])
                            ->default('finished_goods')
                            ->required()
                            ->live()
                            ->afterStateUpdated(function ($state, Forms\Set $set) {
                                // Auto-set defaults based on product type
                                match ($state) {
                                    'raw_material' => [
                                        $set('is_sellable', false),
                                        $set('is_purchasable', true),
                                        $set('primary_unit', 'kg'),
                                        $set('requires_quality_check', true),
                                    ],
                                    'service' => [
                                        $set('is_sellable', true),
                                        $set('is_purchasable', false),
                                        $set('primary_unit', 'jam'),
                                        $set('track_serial_number', false),
                                        $set('track_expiry_date', false),
                                    ],
                                    'work_in_progress' => [
                                        $set('is_sellable', false),
                                        $set('is_purchasable', false),
                                    ],
                                    'consignment' => [
                                        $set('is_sellable', true),
                                        $set('is_purchasable', false),
                                    ],
                                    default => [
                                        $set('is_sellable', true),
                                        $set('is_purchasable', true),
                                        $set('primary_unit', 'pcs'),
                                    ]
                                };
                            }),
                        Forms\Components\TextInput::make('barcode')
                            ->label('Barcode')
                            ->maxLength(255)
                            ->placeholder('Product barcode'),
                        Forms\Components\Textarea::make('description')
                            ->label('Description')
                            ->maxLength(65535)
                            ->columnSpanFull()
                            ->placeholder('Product description'),
                    ])
                    ->columns(2),
                Forms\Components\Section::make('Pricing Information')
                    ->schema([
                        Forms\Components\TextInput::make('cost_price')
                            ->label('Cost Price')
                            ->numeric()
                            ->prefix('Rp')
                            ->placeholder('0'),
                        Forms\Components\TextInput::make('price')
                            ->label('Selling Price')
                            ->numeric()
                            ->prefix('Rp')
                            ->placeholder('0'),
                        Forms\Components\TextInput::make('stock_quantity')
                            ->label('Stock Quantity')
                            ->numeric()
                            ->placeholder('0')
                            ->default(0),
                    ])
                    ->columns(3),
                Forms\Components\Section::make('Manufacturing & Inventory')
                    ->schema([
                        Forms\Components\Toggle::make('is_manufactured')
                            ->label('Manufactured')
                            ->helperText('Apakah produk ini diproduksi sendiri'),
                        Forms\Components\Toggle::make('is_purchasable')
                            ->label('Purchasable')
                            ->helperText('Apakah produk ini bisa dibeli dari supplier')
                            ->default(true),
                        Forms\Components\Toggle::make('is_sellable')
                            ->label('Sellable')
                            ->helperText('Apakah produk ini bisa dijual')
                            ->default(true),
                        Forms\Components\TextInput::make('primary_unit')
                            ->label('Primary Unit')
                            ->placeholder('pcs, kg, meter, jam, dll')
                            ->default('pcs'),
                        Forms\Components\TextInput::make('purchase_unit')
                            ->label('Purchase Unit')
                            ->placeholder('Satuan pembelian (jika berbeda)')
                            ->helperText('Kosongkan jika sama dengan primary unit'),
                        Forms\Components\TextInput::make('purchase_unit_ratio')
                            ->label('Purchase Unit Ratio')
                            ->numeric()
                            ->step(0.0001)
                            ->default(1)
                            ->helperText('Rasio konversi: 1 purchase unit = X primary unit'),
                        Forms\Components\TextInput::make('minimum_stock_level')
                            ->label('Minimum Stock Level')
                            ->numeric()
                            ->step(0.01)
                            ->helperText('Level stok minimum untuk reorder'),
                        Forms\Components\TextInput::make('reorder_quantity')
                            ->label('Reorder Quantity')
                            ->numeric()
                            ->step(0.01)
                            ->helperText('Jumlah reorder otomatis'),
                        Forms\Components\TextInput::make('lead_time_days')
                            ->label('Lead Time (Days)')
                            ->numeric()
                            ->helperText('Lead time dalam hari untuk produksi/pembelian'),
                    ])
                    ->columns(3),
                Forms\Components\Section::make('Quality & Tracking')
                    ->schema([
                        Forms\Components\Toggle::make('track_serial_number')
                            ->label('Track Serial Number')
                            ->helperText('Apakah produk ini perlu tracking serial number'),
                        Forms\Components\Toggle::make('track_expiry_date')
                            ->label('Track Expiry Date')
                            ->helperText('Apakah produk ini perlu tracking tanggal kadaluarsa'),
                        Forms\Components\Toggle::make('requires_quality_check')
                            ->label('Requires Quality Check')
                            ->helperText('Apakah produk ini memerlukan quality check'),
                        Forms\Components\Textarea::make('quality_specifications')
                            ->label('Quality Specifications')
                            ->placeholder('Spesifikasi kualitas produk')
                            ->columnSpanFull()
                            ->rows(3),
                    ])
                    ->columns(3),
                Forms\Components\Section::make('Product Settings')
                    ->schema([
                        Forms\Components\Toggle::make('is_active')
                            ->label('Active')
                            ->default(true),
                        Forms\Components\Toggle::make('is_food_item')
                            ->label('Food Item')
                            ->default(false),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('sku')
                    ->label('SKU')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('name')
                    ->label('Product Name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('category.name')
                    ->label('Category')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('product_type')
                    ->label('Type')
                    ->badge()
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'finished_goods' => 'Barang Jadi',
                        'raw_material' => 'Bahan Baku',
                        'work_in_progress' => 'WIP',
                        'service' => 'Layanan',
                        'consignment' => 'Konsinyasi',
                        'bundle' => 'Bundle',
                        'digital' => 'Digital',
                        'spare_part' => 'Spare Part',
                        default => $state,
                    })
                    ->color(fn(string $state): string => match ($state) {
                        'finished_goods' => 'success',
                        'raw_material' => 'warning',
                        'work_in_progress' => 'info',
                        'service' => 'primary',
                        'consignment' => 'secondary',
                        'bundle' => 'success',
                        'digital' => 'info',
                        'spare_part' => 'gray',
                        default => 'gray',
                    })
                    ->sortable(),
                Tables\Columns\TextColumn::make('barcode')
                    ->label('Barcode')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('cost_price')
                    ->label('Cost Price')
                    ->money('IDR')
                    ->sortable(),
                Tables\Columns\TextColumn::make('price')
                    ->label('Selling Price')
                    ->money('IDR')
                    ->sortable(),
                Tables\Columns\TextColumn::make('stock_quantity')
                    ->label('Stock Quantity')
                    ->badge()
                    ->color(function ($state) {
                        return $state <= 10 ? 'danger' : ($state <= 50 ? 'warning' : 'success');
                    })
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean(),
                Tables\Columns\IconColumn::make('is_food_item')
                    ->label('Food Item')
                    ->boolean()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created At')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('category_id')
                    ->label('Category')
                    ->options(Category::pluck('name', 'id'))
                    ->searchable()
                    ->preload(),
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active Status')
                    ->boolean()
                    ->trueLabel('Active Only')
                    ->falseLabel('Inactive Only')
                    ->native(false),
                Tables\Filters\TernaryFilter::make('is_food_item')
                    ->label('Food Item')
                    ->boolean()
                    ->trueLabel('Food Items Only')
                    ->falseLabel('Non-Food Items Only')
                    ->native(false),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProducts::route('/'),
            'create' => Pages\CreateProduct::route('/create'),
            'view' => Pages\ViewProduct::route('/{record}'),
            'edit' => Pages\EditProduct::route('/{record}/edit'),
        ];
    }
}
