<?php

namespace App\Filament\Manufacturing\Resources;

use App\Filament\Manufacturing\Resources\MaterialConsumptionResource\Pages;
use App\Models\MaterialConsumption;
use App\Models\ProductionOrder;
use App\Models\ProductionStage;
use App\Models\Product;
use App\Models\Warehouse;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class MaterialConsumptionResource extends Resource
{
    protected static ?string $model = MaterialConsumption::class;

    protected static ?string $navigationIcon = 'heroicon-o-minus-circle';

    protected static ?string $navigationGroup = 'Operasi Produksi';

    protected static ?string $navigationLabel = 'Konsumsi Material';

    protected static ?string $modelLabel = 'Konsumsi Material';

    protected static ?string $pluralModelLabel = 'Konsumsi Material';

    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Consumption Information')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Select::make('production_order_id')
                                    ->label('Production Order')
                                    ->options(ProductionOrder::with('product')->get()->mapWithKeys(function ($po) {
                                        return [$po->id => $po->po_number . ' - ' . $po->product->name];
                                    }))
                                    ->searchable()
                                    ->required()
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set) {
                                        if ($state) {
                                            $po = ProductionOrder::find($state);
                                            if ($po) {
                                                $set('warehouse_id', $po->warehouse_id);
                                            }
                                        }
                                    }),

                                Forms\Components\Select::make('production_stage_id')
                                    ->label('Production Stage (Optional)')
                                    ->options(function (callable $get) {
                                        $poId = $get('production_order_id');
                                        if (!$poId) return [];

                                        return ProductionStage::where('production_order_id', $poId)
                                            ->pluck('stage_name', 'id');
                                    })
                                    ->searchable(),
                            ]),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Select::make('product_id')
                                    ->label('Material/Product')
                                    ->options(Product::all()->pluck('name', 'id'))
                                    ->searchable()
                                    ->required()
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set) {
                                        if ($state) {
                                            $product = Product::find($state);
                                            if ($product) {
                                                $set('unit', $product->unit ?? 'pcs');
                                                $set('unit_cost', $product->cost ?? 0);
                                            }
                                        }
                                    }),

                                Forms\Components\Select::make('warehouse_id')
                                    ->label('Warehouse')
                                    ->options(Warehouse::all()->pluck('name', 'id'))
                                    ->searchable()
                                    ->required(),
                            ]),
                    ]),

                Forms\Components\Section::make('Consumption Details')
                    ->schema([
                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\TextInput::make('quantity_consumed')
                                    ->label('Quantity Consumed')
                                    ->numeric()
                                    ->step(0.0001)
                                    ->required()
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                        $unitCost = $get('unit_cost') ?? 0;
                                        $set('total_cost', $state * $unitCost);
                                    }),

                                Forms\Components\TextInput::make('unit')
                                    ->required(),

                                Forms\Components\Select::make('consumption_type')
                                    ->options([
                                        'normal' => 'Normal Consumption',
                                        'waste' => 'Waste',
                                        'rework' => 'Rework',
                                        'adjustment' => 'Adjustment',
                                    ])
                                    ->default('normal')
                                    ->required(),
                            ]),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('unit_cost')
                                    ->label('Unit Cost')
                                    ->numeric()
                                    ->prefix('Rp')
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                        $quantity = $get('quantity_consumed') ?? 0;
                                        $set('total_cost', $quantity * $state);
                                    }),

                                Forms\Components\TextInput::make('total_cost')
                                    ->label('Total Cost')
                                    ->numeric()
                                    ->prefix('Rp')
                                    ->disabled()
                                    ->dehydrated(),
                            ]),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\DateTimePicker::make('consumption_date')
                                    ->required()
                                    ->default(now()),

                                Forms\Components\Select::make('consumed_by')
                                    ->label('Consumed By')
                                    ->options(User::all()->pluck('name', 'id'))
                                    ->searchable()
                                    ->required()
                                    ->default(auth()->id()),
                            ]),
                    ]),

                Forms\Components\Section::make('Additional Information')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('batch_number')
                                    ->label('Batch Number')
                                    ->maxLength(255),

                                Forms\Components\TextInput::make('lot_number')
                                    ->label('Lot Number')
                                    ->maxLength(255),
                            ]),

                        Forms\Components\Textarea::make('reason')
                            ->label('Reason/Notes')
                            ->rows(3)
                            ->columnSpanFull(),
                    ])
                    ->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('productionOrder.po_number')
                    ->label('Production Order')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('productionStage.stage_name')
                    ->label('Stage')
                    ->searchable(),

                Tables\Columns\TextColumn::make('product.sku')
                    ->label('Product Code')
                    ->searchable(),

                Tables\Columns\TextColumn::make('product.name')
                    ->label('Material')
                    ->searchable()
                    ->wrap(),

                Tables\Columns\TextColumn::make('quantity_consumed')
                    ->label('Quantity')
                    ->numeric(decimalPlaces: 4)
                    ->sortable(),

                Tables\Columns\TextColumn::make('unit'),

                Tables\Columns\TextColumn::make('consumption_type')
                    ->label('Type')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'normal' => 'success',
                        'waste' => 'danger',
                        'rework' => 'warning',
                        'adjustment' => 'info',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('unit_cost')
                    ->label('Unit Cost')
                    ->money('IDR')
                    ->sortable(),

                Tables\Columns\TextColumn::make('total_cost')
                    ->label('Total Cost')
                    ->money('IDR')
                    ->sortable(),

                Tables\Columns\TextColumn::make('warehouse.name')
                    ->label('Warehouse')
                    ->searchable(),

                Tables\Columns\TextColumn::make('consumer.name')
                    ->label('Consumed By')
                    ->searchable(),

                Tables\Columns\TextColumn::make('consumption_date')
                    ->label('Date')
                    ->dateTime()
                    ->sortable(),

                Tables\Columns\TextColumn::make('batch_number')
                    ->label('Batch')
                    ->searchable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('consumption_type')
                    ->options([
                        'normal' => 'Normal',
                        'waste' => 'Waste',
                        'rework' => 'Rework',
                        'adjustment' => 'Adjustment',
                    ]),

                Tables\Filters\SelectFilter::make('production_order_id')
                    ->label('Production Order')
                    ->options(ProductionOrder::with('product')->get()->mapWithKeys(function ($po) {
                        return [$po->id => $po->po_number . ' - ' . $po->product->name];
                    }))
                    ->searchable(),

                Tables\Filters\Filter::make('consumption_date')
                    ->form([
                        Forms\Components\DatePicker::make('consumed_from'),
                        Forms\Components\DatePicker::make('consumed_until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['consumed_from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('consumption_date', '>=', $date),
                            )
                            ->when(
                                $data['consumed_until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('consumption_date', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('consumption_date', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMaterialConsumptions::route('/'),
            'create' => Pages\CreateMaterialConsumption::route('/create'),
            'view' => Pages\ViewMaterialConsumption::route('/{record}'),
            'edit' => Pages\EditMaterialConsumption::route('/{record}/edit'),
        ];
    }
}
