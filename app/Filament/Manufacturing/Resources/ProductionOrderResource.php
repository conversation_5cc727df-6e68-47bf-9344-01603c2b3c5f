<?php

namespace App\Filament\Manufacturing\Resources;

use App\Filament\Manufacturing\Resources\ProductionOrderResource\Pages;
use App\Filament\Manufacturing\Resources\ProductionOrderResource\RelationManagers;
use App\Models\ProductionOrder;
use App\Models\Bom;
use App\Models\Product;
use App\Models\Warehouse;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ProductionOrderResource extends Resource
{
    protected static ?string $model = ProductionOrder::class;

    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';

    protected static ?string $navigationGroup = 'Operasi Produksi';

    protected static ?string $navigationLabel = 'Order Produksi';

    protected static ?string $modelLabel = 'Order Produksi';

    protected static ?string $pluralModelLabel = 'Order Produksi';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Order Information')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('po_number')
                                    ->label('PO Number')
                                    ->disabled()
                                    ->dehydrated(false),

                                Forms\Components\Select::make('status')
                                    ->options([
                                        'planned' => 'Planned',
                                        'released' => 'Released',
                                        'in_progress' => 'In Progress',
                                        'completed' => 'Completed',
                                        'cancelled' => 'Cancelled',
                                        'on_hold' => 'On Hold',
                                    ])
                                    ->default('planned')
                                    ->required(),
                            ]),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Select::make('bom_id')
                                    ->label('Bill of Materials')
                                    ->options(Bom::with('product')->get()->mapWithKeys(function ($bom) {
                                        return [$bom->id => $bom->name . ' (' . $bom->product->name . ')'];
                                    }))
                                    ->searchable()
                                    ->required()
                                    ->reactive()
                                    ->afterStateUpdated(function ($state, callable $set) {
                                        if ($state) {
                                            $bom = Bom::find($state);
                                            if ($bom) {
                                                $set('product_id', $bom->product_id);
                                                $set('unit', $bom->unit);
                                            }
                                        }
                                    }),

                                Forms\Components\Select::make('product_id')
                                    ->label('Product')
                                    ->options(Product::all()->pluck('name', 'id'))
                                    ->searchable()
                                    ->required()
                                    ->disabled()
                                    ->dehydrated(),
                            ]),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Select::make('warehouse_id')
                                    ->label('Warehouse')
                                    ->options(Warehouse::all()->pluck('name', 'id'))
                                    ->searchable()
                                    ->required(),

                                Forms\Components\Select::make('priority')
                                    ->options([
                                        'low' => 'Low',
                                        'normal' => 'Normal',
                                        'high' => 'High',
                                        'urgent' => 'Urgent',
                                    ])
                                    ->default('normal')
                                    ->required(),
                            ]),
                    ]),

                Forms\Components\Section::make('Quantity & Schedule')
                    ->schema([
                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\TextInput::make('planned_quantity')
                                    ->label('Planned Quantity')
                                    ->numeric()
                                    ->required(),

                                Forms\Components\TextInput::make('unit')
                                    ->disabled()
                                    ->dehydrated(),

                                Forms\Components\TextInput::make('actual_quantity')
                                    ->label('Actual Quantity')
                                    ->numeric()
                                    ->default(0),
                            ]),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\DatePicker::make('planned_start_date')
                                    ->required(),

                                Forms\Components\DatePicker::make('planned_end_date')
                                    ->required(),
                            ]),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\DateTimePicker::make('actual_start_date'),

                                Forms\Components\DateTimePicker::make('actual_end_date'),
                            ]),
                    ]),

                Forms\Components\Section::make('Cost Planning')
                    ->schema([
                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\TextInput::make('planned_material_cost')
                                    ->label('Planned Material Cost')
                                    ->numeric()
                                    ->prefix('Rp')
                                    ->default(0),

                                Forms\Components\TextInput::make('planned_labor_cost')
                                    ->label('Planned Labor Cost')
                                    ->numeric()
                                    ->prefix('Rp')
                                    ->default(0),

                                Forms\Components\TextInput::make('planned_overhead_cost')
                                    ->label('Planned Overhead Cost')
                                    ->numeric()
                                    ->prefix('Rp')
                                    ->default(0),
                            ]),

                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\TextInput::make('actual_material_cost')
                                    ->label('Actual Material Cost')
                                    ->numeric()
                                    ->prefix('Rp')
                                    ->default(0),

                                Forms\Components\TextInput::make('actual_labor_cost')
                                    ->label('Actual Labor Cost')
                                    ->numeric()
                                    ->prefix('Rp')
                                    ->default(0),

                                Forms\Components\TextInput::make('actual_overhead_cost')
                                    ->label('Actual Overhead Cost')
                                    ->numeric()
                                    ->prefix('Rp')
                                    ->default(0),
                            ]),
                    ])
                    ->collapsible(),

                Forms\Components\Section::make('Assignment & Notes')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Select::make('assigned_to')
                                    ->label('Assigned To')
                                    ->options(User::all()->pluck('name', 'id'))
                                    ->searchable(),

                                Forms\Components\Select::make('created_by')
                                    ->label('Created By')
                                    ->options(User::all()->pluck('name', 'id'))
                                    ->default(auth()->id())
                                    ->required(),
                            ]),

                        Forms\Components\Textarea::make('notes')
                            ->rows(3)
                            ->columnSpanFull(),
                    ])
                    ->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('po_number')
                    ->label('PO Number')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('product.name')
                    ->label('Product')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('bom.name')
                    ->label('BOM')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('planned_quantity')
                    ->label('Planned Qty')
                    ->numeric()
                    ->sortable(),

                Tables\Columns\TextColumn::make('actual_quantity')
                    ->label('Actual Qty')
                    ->numeric()
                    ->sortable(),

                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'planned' => 'gray',
                        'released' => 'info',
                        'in_progress' => 'warning',
                        'completed' => 'success',
                        'cancelled' => 'danger',
                        'on_hold' => 'secondary',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('priority')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'low' => 'gray',
                        'normal' => 'primary',
                        'high' => 'warning',
                        'urgent' => 'danger',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('assignedUser.name')
                    ->label('Assigned To')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('planned_start_date')
                    ->label('Start Date')
                    ->date()
                    ->sortable(),

                Tables\Columns\TextColumn::make('planned_end_date')
                    ->label('End Date')
                    ->date()
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'planned' => 'Planned',
                        'released' => 'Released',
                        'in_progress' => 'In Progress',
                        'completed' => 'Completed',
                        'cancelled' => 'Cancelled',
                        'on_hold' => 'On Hold',
                    ]),

                Tables\Filters\SelectFilter::make('priority')
                    ->options([
                        'low' => 'Low',
                        'normal' => 'Normal',
                        'high' => 'High',
                        'urgent' => 'Urgent',
                    ]),

                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\ProductionOrderMaterialsRelationManager::class,
            RelationManagers\ProductionStagesRelationManager::class,
            RelationManagers\QualityInspectionsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProductionOrders::route('/'),
            'create' => Pages\CreateProductionOrder::route('/create'),
            'view' => Pages\ViewProductionOrder::route('/{record}'),
            'edit' => Pages\EditProductionOrder::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
