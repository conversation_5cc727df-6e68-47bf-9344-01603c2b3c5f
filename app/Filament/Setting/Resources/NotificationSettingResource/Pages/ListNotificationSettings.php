<?php

namespace App\Filament\Setting\Resources\NotificationSettingResource\Pages;

use App\Filament\Setting\Resources\NotificationSettingResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListNotificationSettings extends ListRecords
{
  protected static string $resource = NotificationSettingResource::class;

  protected function getHeaderActions(): array
  {
    return [
      Actions\CreateAction::make(),
    ];
  }
}
