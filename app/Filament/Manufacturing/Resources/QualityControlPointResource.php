<?php

namespace App\Filament\Manufacturing\Resources;

use App\Filament\Manufacturing\Resources\QualityControlPointResource\Pages;
use App\Models\QualityControlPoint;
use App\Models\Product;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class QualityControlPointResource extends Resource
{
    protected static ?string $model = QualityControlPoint::class;

    protected static ?string $navigationIcon = 'heroicon-o-shield-check';

    protected static ?string $navigationGroup = 'Data Master';

    protected static ?string $navigationLabel = 'Titik Kontrol Kualitas';

    protected static ?string $modelLabel = 'Titik QC';

    protected static ?string $pluralModelLabel = 'Titik QC';

    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('QC Point Information')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('name')
                                    ->required()
                                    ->maxLength(255),

                                Forms\Components\Select::make('type')
                                    ->options([
                                        'incoming' => 'Incoming Inspection',
                                        'in_process' => 'In-Process Inspection',
                                        'final' => 'Final Inspection',
                                        'outgoing' => 'Outgoing Inspection',
                                    ])
                                    ->default('in_process')
                                    ->required(),
                            ]),

                        Forms\Components\Textarea::make('description')
                            ->rows(3)
                            ->columnSpanFull(),
                    ]),

                Forms\Components\Section::make('Configuration')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Select::make('product_id')
                                    ->label('Specific Product (Optional)')
                                    ->options(Product::all()->pluck('name', 'id'))
                                    ->searchable()
                                    ->placeholder('Apply to all products'),

                                Forms\Components\TextInput::make('sequence')
                                    ->numeric()
                                    ->default(0)
                                    ->required(),
                            ]),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\Toggle::make('is_mandatory')
                                    ->label('Mandatory Check')
                                    ->default(true),

                                Forms\Components\Select::make('status')
                                    ->options([
                                        'active' => 'Active',
                                        'inactive' => 'Inactive',
                                    ])
                                    ->default('active')
                                    ->required(),
                            ]),
                    ]),

                Forms\Components\Section::make('Test Parameters')
                    ->schema([
                        Forms\Components\Grid::make(3)
                            ->schema([
                                Forms\Components\TextInput::make('min_acceptable_value')
                                    ->label('Min Acceptable Value')
                                    ->numeric()
                                    ->step(0.0001),

                                Forms\Components\TextInput::make('max_acceptable_value')
                                    ->label('Max Acceptable Value')
                                    ->numeric()
                                    ->step(0.0001),

                                Forms\Components\TextInput::make('unit_of_measure')
                                    ->label('Unit of Measure')
                                    ->maxLength(20),
                            ]),

                        Forms\Components\Textarea::make('instructions')
                            ->label('Test Instructions')
                            ->rows(4)
                            ->columnSpanFull(),
                    ])
                    ->collapsible(),

                Forms\Components\Section::make('Assignment')
                    ->schema([
                        Forms\Components\Select::make('created_by')
                            ->label('Created By')
                            ->options(User::all()->pluck('name', 'id'))
                            ->default(auth()->id())
                            ->required(),
                    ])
                    ->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'incoming' => 'info',
                        'in_process' => 'warning',
                        'final' => 'success',
                        'outgoing' => 'primary',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('product.name')
                    ->label('Product')
                    ->searchable()
                    ->placeholder('All Products'),

                Tables\Columns\TextColumn::make('sequence')
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_mandatory')
                    ->label('Mandatory')
                    ->boolean(),

                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'active' => 'success',
                        'inactive' => 'gray',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('min_acceptable_value')
                    ->label('Min Value')
                    ->numeric(decimalPlaces: 4),

                Tables\Columns\TextColumn::make('max_acceptable_value')
                    ->label('Max Value')
                    ->numeric(decimalPlaces: 4),

                Tables\Columns\TextColumn::make('unit_of_measure')
                    ->label('Unit'),

                Tables\Columns\TextColumn::make('creator.name')
                    ->label('Created By')
                    ->searchable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->options([
                        'incoming' => 'Incoming',
                        'in_process' => 'In-Process',
                        'final' => 'Final',
                        'outgoing' => 'Outgoing',
                    ]),

                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'active' => 'Active',
                        'inactive' => 'Inactive',
                    ]),

                Tables\Filters\TernaryFilter::make('is_mandatory')
                    ->label('Mandatory'),

                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ])
            ->defaultSort('sequence');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListQualityControlPoints::route('/'),
            'create' => Pages\CreateQualityControlPoint::route('/create'),
            'view' => Pages\ViewQualityControlPoint::route('/{record}'),
            'edit' => Pages\EditQualityControlPoint::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
