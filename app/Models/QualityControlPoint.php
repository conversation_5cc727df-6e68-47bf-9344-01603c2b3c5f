<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class QualityControlPoint extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'type',
        'product_id',
        'is_mandatory',
        'sequence',
        'test_parameters',
        'min_acceptable_value',
        'max_acceptable_value',
        'unit_of_measure',
        'status',
        'instructions',
        'created_by',
    ];

    protected $casts = [
        'is_mandatory' => 'boolean',
        'test_parameters' => 'array',
        'min_acceptable_value' => 'decimal:4',
        'max_acceptable_value' => 'decimal:4',
    ];

    // Relationships
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function qualityInspections(): HasMany
    {
        return $this->hasMany(QualityInspection::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeMandatory($query)
    {
        return $query->where('is_mandatory', true);
    }

    public function scopeForProduct($query, $productId)
    {
        return $query->where('product_id', $productId);
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    public function scopeOrderedBySequence($query)
    {
        return $query->orderBy('sequence');
    }

    // Methods
    public function isValueAcceptable(float $value): bool
    {
        if ($this->min_acceptable_value !== null && $value < $this->min_acceptable_value) {
            return false;
        }
        
        if ($this->max_acceptable_value !== null && $value > $this->max_acceptable_value) {
            return false;
        }
        
        return true;
    }

    public function getAcceptableRange(): string
    {
        if ($this->min_acceptable_value !== null && $this->max_acceptable_value !== null) {
            return "{$this->min_acceptable_value} - {$this->max_acceptable_value} {$this->unit_of_measure}";
        }
        
        if ($this->min_acceptable_value !== null) {
            return "≥ {$this->min_acceptable_value} {$this->unit_of_measure}";
        }
        
        if ($this->max_acceptable_value !== null) {
            return "≤ {$this->max_acceptable_value} {$this->unit_of_measure}";
        }
        
        return 'No specific range defined';
    }

    public function getPassRate(): float
    {
        $totalInspections = $this->qualityInspections()->count();
        
        if ($totalInspections === 0) {
            return 0;
        }
        
        $passedInspections = $this->qualityInspections()
            ->where('overall_result', 'passed')
            ->count();
            
        return ($passedInspections / $totalInspections) * 100;
    }
}
