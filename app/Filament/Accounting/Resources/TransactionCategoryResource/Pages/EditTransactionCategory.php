<?php

namespace App\Filament\Accounting\Resources\TransactionCategoryResource\Pages;

use App\Filament\Accounting\Resources\TransactionCategoryResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditTransactionCategory extends EditRecord
{
    protected static string $resource = TransactionCategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make()
                ->label('Lihat Kategori')
                ->icon('heroicon-o-eye'),
            Actions\DeleteAction::make()
                ->label('Hapus Kategori')
                ->icon('heroicon-o-trash'),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getSavedNotificationTitle(): ?string
    {
        return 'Kategori transaksi berhasil diperbarui';
    }
}
