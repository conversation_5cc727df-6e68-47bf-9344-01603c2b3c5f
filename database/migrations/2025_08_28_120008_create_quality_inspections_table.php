<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('quality_inspections', function (Blueprint $table) {
            $table->id();
            $table->string('inspection_number')->unique();
            $table->foreignId('production_order_id')->constrained('production_orders')->onDelete('cascade');
            $table->foreignId('production_stage_id')->nullable()->constrained('production_stages')->onDelete('set null');
            $table->foreignId('quality_control_point_id')->constrained('quality_control_points');
            $table->decimal('quantity_inspected', 10, 2);
            $table->decimal('quantity_passed', 10, 2)->default(0);
            $table->decimal('quantity_failed', 10, 2)->default(0);
            $table->decimal('quantity_rework', 10, 2)->default(0);
            $table->enum('overall_result', ['pending', 'passed', 'failed', 'conditional_pass'])->default('pending');
            $table->datetime('inspection_date');
            $table->foreignId('inspector_id')->constrained('users');
            $table->text('notes')->nullable();
            $table->text('corrective_actions')->nullable();
            $table->json('inspection_data')->nullable(); // Flexible data for test results
            $table->boolean('requires_rework')->default(false);
            $table->boolean('is_final_inspection')->default(false);
            $table->timestamps();

            // Indexes
            $table->index(['production_order_id', 'inspection_date'], 'qi_po_date_idx');
            $table->index(['quality_control_point_id', 'overall_result'], 'qi_qcp_result_idx');
            $table->index(['inspector_id', 'inspection_date'], 'qi_inspector_date_idx');
            $table->index('inspection_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('quality_inspections');
    }
};
