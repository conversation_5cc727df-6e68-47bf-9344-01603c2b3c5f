<?php

namespace Database\Seeders;

use App\Models\Karyawan;
use App\Models\EventManager;
use App\Models\NotificationSetting;
use Filament\Notifications\Notification;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class EventWAExpenseRequestCreatedSeed extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $karyawan = Karyawan::first();

        $event = EventManager::create([
            'event_name' => 'expense_request_created',
            'description' => 'ketika expense request dibuat',
            'message' => "💵 *Permintaan Biaya Baru* 💵\n\nHalo Bpk/Ibu *{penerima}*,\nAda permintaan biaya baru dari staf Anda, *{pengirim}* yang membutuhkan persetujuan Anda.\n\n🧾 Detail Permintaan:\nNo. Request: *{no_req}*\nTipe: *{judul}*\nJumlah: *{jumlah}*\n\nMohon segera ditinjau melalui link berikut:\n{url}"
        ]);

        NotificationSetting::create([
            'event_id' => $event->id,
            'user_id' => $karyawan->id,
            'channel' => 'whatsapp',
            'is_active' => 1,
        ]);

        $karyawan->nomor_telepon = '082286230830';
        $karyawan->save();
    }
}
