<?php

namespace App\Filament\Manufacturing\Resources\ProductionPlanResource\RelationManagers;

use App\Models\Bom;
use App\Models\Product;
use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class ProductionOrdersRelationManager extends RelationManager
{
    protected static string $relationship = 'productionOrders';

    protected static ?string $recordTitleAttribute = 'po_number';

    protected static ?string $title = 'Production Orders';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\Select::make('bom_id')
                            ->label('Bill of Materials')
                            ->options(Bom::with('product')->get()->mapWithKeys(function ($bom) {
                                return [$bom->id => $bom->name . ' (' . $bom->product->name . ')'];
                            }))
                            ->searchable()
                            ->required(),

                        Forms\Components\Select::make('product_id')
                            ->label('Product')
                            ->options(Product::all()->pluck('name', 'id'))
                            ->searchable()
                            ->required(),
                    ]),

                Forms\Components\Grid::make(3)
                    ->schema([
                        Forms\Components\TextInput::make('planned_quantity')
                            ->label('Planned Quantity')
                            ->numeric()
                            ->required(),

                        Forms\Components\TextInput::make('unit')
                            ->default('pcs')
                            ->required(),

                        Forms\Components\Select::make('priority')
                            ->options([
                                'low' => 'Low',
                                'normal' => 'Normal',
                                'high' => 'High',
                                'urgent' => 'Urgent',
                            ])
                            ->default('normal')
                            ->required(),
                    ]),

                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\DatePicker::make('planned_start_date')
                            ->required(),

                        Forms\Components\DatePicker::make('planned_end_date')
                            ->required(),
                    ]),

                Forms\Components\Select::make('assigned_to')
                    ->label('Assigned To')
                    ->options(User::all()->pluck('name', 'id'))
                    ->searchable(),

                Forms\Components\Textarea::make('notes')
                    ->rows(2)
                    ->columnSpanFull(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('po_number')
            ->columns([
                Tables\Columns\TextColumn::make('po_number')
                    ->label('PO Number')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('product.name')
                    ->label('Product')
                    ->searchable()
                    ->wrap(),

                Tables\Columns\TextColumn::make('bom.name')
                    ->label('BOM')
                    ->searchable(),

                Tables\Columns\TextColumn::make('planned_quantity')
                    ->label('Planned Qty')
                    ->numeric(),

                Tables\Columns\TextColumn::make('actual_quantity')
                    ->label('Actual Qty')
                    ->numeric(),

                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'planned' => 'gray',
                        'released' => 'info',
                        'in_progress' => 'warning',
                        'completed' => 'success',
                        'cancelled' => 'danger',
                        'on_hold' => 'secondary',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('priority')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'low' => 'gray',
                        'normal' => 'primary',
                        'high' => 'warning',
                        'urgent' => 'danger',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('assignedUser.name')
                    ->label('Assigned To')
                    ->searchable(),

                Tables\Columns\TextColumn::make('planned_start_date')
                    ->label('Start Date')
                    ->date(),

                Tables\Columns\TextColumn::make('planned_end_date')
                    ->label('End Date')
                    ->date(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'planned' => 'Planned',
                        'released' => 'Released',
                        'in_progress' => 'In Progress',
                        'completed' => 'Completed',
                        'cancelled' => 'Cancelled',
                        'on_hold' => 'On Hold',
                    ]),

                Tables\Filters\SelectFilter::make('priority')
                    ->options([
                        'low' => 'Low',
                        'normal' => 'Normal',
                        'high' => 'High',
                        'urgent' => 'Urgent',
                    ]),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->mutateFormDataUsing(function (array $data): array {
                        $data['warehouse_id'] = $this->getOwnerRecord()->warehouse_id;
                        $data['created_by'] = auth()->id();
                        return $data;
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->url(fn ($record) => route('filament.manufacturing.resources.production-orders.view', $record)),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
