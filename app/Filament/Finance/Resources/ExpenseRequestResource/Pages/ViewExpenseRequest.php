<?php

namespace App\Filament\Finance\Resources\ExpenseRequestResource\Pages;

use App\Filament\Finance\Resources\ExpenseRequestResource;
use App\Models\ExpenseRequest;
use App\Models\PaymentMethod;
use App\Services\ExpenseRequestService;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;
use Filament\Forms;

class ViewExpenseRequest extends ViewRecord
{
    protected static string $resource = ExpenseRequestResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->visible(fn() => $this->getRecord()->isEditable()),
            Actions\Action::make('submit')
                ->label('Submit')
                ->icon('heroicon-o-paper-airplane')
                ->color('warning')
                ->visible(fn() => $this->getRecord()->canBeSubmitted())
                ->requiresConfirmation()
                ->action(function () {
                    $record = $this->getRecord();
                    $record->submit();

                    $this->refreshFormData(['status']);
                }),
            Actions\Action::make('approve')
                ->label('Setujui')
                ->icon('heroicon-o-check')
                ->color('success')
                ->visible(function () {
                    $record = $this->getRecord();
                    return $record->canApproving();
                })
                ->requiresConfirmation()
                ->action(function () {
                    $record = $this->getRecord();
                    $record->submit($record->approvals()->first()->step + 1);

                    $this->refreshFormData(['status']);
                }),
            Actions\Action::make('reject')
                ->label('Tolak')
                ->icon('heroicon-o-x-mark')
                ->color('danger')
                ->visible(fn() => $this->getRecord()->canRejecting())
                ->form([
                    Forms\Components\Textarea::make('rejection_reason')
                        ->label('Alasan Penolakan')
                        ->required()
                        ->rows(3),
                ])
                ->action(function (array $data) {
                    $record = $this->getRecord();
                    $record->reject($data['rejection_reason']);

                    $this->refreshFormData(['status']);
                }),
            Actions\Action::make('revision')
                ->label('Revisi')
                ->icon('heroicon-o-pencil-square')
                ->color('info')
                ->visible(fn() => $this->getRecord()->canRevising())
                ->form([
                    Forms\Components\Textarea::make('revision_reason')
                        ->label('Alasan Revisi')
                        ->required()
                        ->rows(3),
                ])
                ->action(function (array $data) {
                    $record = $this->getRecord();
                    $record->revision($data['revision_reason']);

                    $this->refreshFormData(['status']);
                }),
            Actions\Action::make('revised')
                ->label('Tandai Sudah Revisi')
                ->icon('heroicon-o-document-check')
                ->color('info')
                ->visible(fn() => $this->getRecord()->approvals()?->first()?->status == 'Revision')
                ->requiresConfirmation()
                ->action(function (array $data) {
                    $eventManager = \App\Models\EventManager::where('event_name', 'expense_request_revised')->first();
                    if ($eventManager) {
                        $service = new ExpenseRequestService();
                        $service->handleRevisionNotification($this->getRecord(), $eventManager->id);
                    }
                }),
            Actions\Action::make('pay')
                ->label('Bayar')
                ->icon('heroicon-o-banknotes')
                ->color('primary')
                ->visible(fn() => $this->getRecord()->canBePaid())
                ->form([
                    Forms\Components\Select::make('payment_method')
                        ->label('Metode Pembayaran')
                        ->required()
                        ->options(
                            PaymentMethod::all()
                                ->mapWithKeys(function ($method) {
                                    return [
                                        $method->id => "{$method->method_display_name} | {$method->bank_name} - {$method->account_number} - {$method->account_name}",
                                    ];
                                })
                        )
                        ->searchable()
                        ->placeholder('Pilih Metode Pembayaran'),
                ])
                ->action(function (array $data) {
                    $record = $this->getRecord();
                    $record->pay($data['payment_method']);

                    $this->refreshFormData(['status']);
                }),
        ];
    }
}
