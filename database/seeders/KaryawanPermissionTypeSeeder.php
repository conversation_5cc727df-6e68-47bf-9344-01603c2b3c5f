<?php

namespace Database\Seeders;

use App\Models\KaryawanPermissionType;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class KaryawanPermissionTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $types = [
            'approve_cuti' => 'Approve Cuti/Izin/Sakit',
            'view_absensi' => 'Lihat Absensi',
            'manage_jadwal' => 'Kelola Jadwal',
            'view_payroll' => 'Lihat Payroll',
            'manage_karyawan' => 'Kelola Data Karyawan',
            'view_reports' => 'Lihat Laporan',
            'manage_overtime' => 'Kelola Lembur',
            'approve_overtime' => 'Approve Lembur',
        ];

        foreach ($types as $name => $description) {
            KaryawanPermissionType::create([
                'name' => $name,
                'description' => $description,
            ]);
        }
    }
}
