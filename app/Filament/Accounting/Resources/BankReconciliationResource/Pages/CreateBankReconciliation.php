<?php

namespace App\Filament\Accounting\Resources\BankReconciliationResource\Pages;

use App\Filament\Accounting\Resources\BankReconciliationResource;
use Filament\Resources\Pages\CreateRecord;

class CreateBankReconciliation extends CreateRecord
{
    protected static string $resource = BankReconciliationResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getCreatedNotificationTitle(): ?string
    {
        return 'Rekonsiliasi bank berhasil dibuat';
    }

    protected function afterCreate(): void
    {
        // Redirect to reconcile page after creation
        $this->redirect($this->getResource()::getUrl('reconcile', ['record' => $this->record]));
    }
}
