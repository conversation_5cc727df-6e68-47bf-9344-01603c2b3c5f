<?php

namespace App\Filament\Manufacturing\Resources\QualityControlPointResource\Pages;

use App\Filament\Manufacturing\Resources\QualityControlPointResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListQualityControlPoints extends ListRecords
{
    protected static string $resource = QualityControlPointResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
