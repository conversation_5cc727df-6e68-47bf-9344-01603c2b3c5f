<?php

namespace App\Filament\Accounting\Resources\ExpenseCategoryResource\Pages;

use App\Filament\Accounting\Resources\ExpenseCategoryResource;
use Filament\Actions;
use Filament\Resources\Pages\ViewRecord;

class ViewExpenseCategory extends ViewRecord
{
    protected static string $resource = ExpenseCategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make()
                ->label('Edit Kategori')
                ->icon('heroicon-o-pencil'),
            Actions\DeleteAction::make()
                ->label('Hapus Kategori')
                ->icon('heroicon-o-trash'),
        ];
    }
}
