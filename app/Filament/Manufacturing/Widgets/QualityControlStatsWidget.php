<?php

namespace App\Filament\Manufacturing\Widgets;

use App\Models\QualityInspection;
use Filament\Widgets\ChartWidget;

class QualityControlStatsWidget extends ChartWidget
{
    protected static ?string $heading = 'Tren Kontrol Kualitas (30 Hari <PERSON>)';

    protected function getData(): array
    {
        $days = collect(range(0, 29))->map(function ($day) {
            return now()->subDays($day)->format('Y-m-d');
        })->reverse()->values();

        $passedData = [];
        $failedData = [];
        $labels = [];

        foreach ($days as $date) {
            $passed = QualityInspection::whereDate('inspection_date', $date)
                ->where('overall_result', 'passed')
                ->count();

            $failed = QualityInspection::whereDate('inspection_date', $date)
                ->where('overall_result', 'failed')
                ->count();

            $passedData[] = $passed;
            $failedData[] = $failed;
            $labels[] = now()->createFromFormat('Y-m-d', $date)->format('M j');
        }

        return [
            'datasets' => [
                [
                    'label' => 'Passed',
                    'data' => $passedData,
                    'backgroundColor' => 'rgba(16, 185, 129, 0.2)',
                    'borderColor' => 'rgb(16, 185, 129)',
                    'borderWidth' => 2,
                    'fill' => true,
                ],
                [
                    'label' => 'Failed',
                    'data' => $failedData,
                    'backgroundColor' => 'rgba(239, 68, 68, 0.2)',
                    'borderColor' => 'rgb(239, 68, 68)',
                    'borderWidth' => 2,
                    'fill' => true,
                ],
            ],
            'labels' => $labels,
        ];
    }

    protected function getType(): string
    {
        return 'line';
    }

    protected function getOptions(): array
    {
        return [
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
            ],
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'ticks' => [
                        'stepSize' => 1,
                    ],
                ],
            ],
            'responsive' => true,
            'maintainAspectRatio' => false,
        ];
    }
}
