<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProductionOrderMaterial extends Model
{
    use HasFactory;

    protected $fillable = [
        'production_order_id',
        'product_id',
        'required_quantity',
        'allocated_quantity',
        'consumed_quantity',
        'unit',
        'unit_cost',
        'total_cost',
        'status',
        'is_critical',
        'required_date',
        'warehouse_id',
        'notes',
    ];

    protected $casts = [
        'required_quantity' => 'decimal:4',
        'allocated_quantity' => 'decimal:4',
        'consumed_quantity' => 'decimal:4',
        'unit_cost' => 'decimal:2',
        'total_cost' => 'decimal:2',
        'is_critical' => 'boolean',
        'required_date' => 'date',
    ];

    // Relationships
    public function productionOrder(): BelongsTo
    {
        return $this->belongsTo(ProductionOrder::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function warehouse(): BelongsTo
    {
        return $this->belongsTo(Warehouse::class);
    }

    // Scopes
    public function scopeCritical($query)
    {
        return $query->where('is_critical', true);
    }

    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeShortage($query)
    {
        return $query->whereColumn('allocated_quantity', '<', 'required_quantity');
    }

    // Accessors
    public function getRemainingQuantityAttribute(): float
    {
        return $this->required_quantity - $this->consumed_quantity;
    }

    public function getAllocationPercentageAttribute(): float
    {
        if ($this->required_quantity == 0) {
            return 0;
        }
        return ($this->allocated_quantity / $this->required_quantity) * 100;
    }

    public function getConsumptionPercentageAttribute(): float
    {
        if ($this->required_quantity == 0) {
            return 0;
        }
        return ($this->consumed_quantity / $this->required_quantity) * 100;
    }

    // Methods
    public function allocate(float $quantity): bool
    {
        $availableStock = $this->getAvailableStock();
        
        if ($quantity > $availableStock) {
            return false;
        }

        $this->update([
            'allocated_quantity' => min($quantity, $this->required_quantity),
            'status' => $this->allocated_quantity >= $this->required_quantity ? 'allocated' : 'pending',
        ]);

        return true;
    }

    public function consume(float $quantity): bool
    {
        if ($quantity > $this->allocated_quantity) {
            return false;
        }

        $newConsumedQuantity = $this->consumed_quantity + $quantity;
        
        $this->update([
            'consumed_quantity' => $newConsumedQuantity,
            'status' => $this->getStatusBasedOnConsumption($newConsumedQuantity),
        ]);

        // Reduce inventory stock
        $this->reduceInventoryStock($quantity);

        return true;
    }

    protected function getStatusBasedOnConsumption(float $consumedQuantity): string
    {
        if ($consumedQuantity >= $this->required_quantity) {
            return 'fully_consumed';
        } elseif ($consumedQuantity > 0) {
            return 'partially_consumed';
        } else {
            return $this->allocated_quantity > 0 ? 'allocated' : 'pending';
        }
    }

    protected function getAvailableStock(): float
    {
        $inventoryStock = InventoryStock::where([
            'product_id' => $this->product_id,
            'warehouse_id' => $this->warehouse_id,
        ])->first();

        return $inventoryStock ? $inventoryStock->available_quantity : 0;
    }

    protected function reduceInventoryStock(float $quantity): void
    {
        $inventoryStock = InventoryStock::where([
            'product_id' => $this->product_id,
            'warehouse_id' => $this->warehouse_id,
        ])->first();

        if ($inventoryStock) {
            $inventoryStock->decrement('quantity', $quantity);
        }
    }

    protected static function boot()
    {
        parent::boot();

        static::saving(function ($material) {
            // Auto-calculate total cost
            $material->total_cost = $material->required_quantity * $material->unit_cost;
        });
    }
}
