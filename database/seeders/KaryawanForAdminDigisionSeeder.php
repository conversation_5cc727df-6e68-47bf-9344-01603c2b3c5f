<?php

namespace Database\Seeders;

use App\Models\Karyawan;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class KaryawanForAdminDigisionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Karyawan::create([
            'nama_lengkap' => 'Admin Digision',
            'nip' => '123456',
            'nik' => '3501234567890001',
            'nomor_kk' => '3501234567890002',
            'agama' => 'Islam',
            'jenis_kelamin' => 'Laki-laki',
            'status_pernikahan' => 'Belum Menikah',
            'jumlah_anak' => 0,
            'kota_lahir' => 'Pekanbaru',
            'tanggal_lahir' => '2001-01-01',
            'alamat' => 'Jl. Mawar No. 123',
            'alamat_ktp' => 'Jl. Melati No. 456',
            'nama_ibu_kandung' => 'Ibu X',
            'golongan_darah' => 'O',
            'nomor_telepon' => '082286230830',
            'id_entitas' => null,
            'outlet_id' => null,
            'id_departemen' => null,
            'id_divisi' => null,
            'id_jabatan' => null,
            'id_user' => 1,
            'supervisor_id' => null,
            'status_aktif' => 1,
            'foto_profil' => 'default.jpg',
            'email' => '<EMAIL>',
            'created_by' => null,
        ]);
    }
}
