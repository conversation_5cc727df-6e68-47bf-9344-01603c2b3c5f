<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\ExpenseRequest;
use App\Models\ExpenseRequestItem;
use App\Models\ExpenseCategory;
use App\Models\Karyawan;
use App\Models\Entitas;
use App\Models\Akun;
use App\Models\User;
use App\Models\Journal;
use App\Models\JournalEntry;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class ExpenseRequestWorkflowTest extends TestCase
{
    use WithFaker;

    /**
     * Test complete expense request workflow
     */
    public function test_complete_expense_request_workflow()
    {
        // Get required data
        $user = User::first();
        $employee = Karyawan::first();
        $entitas = Entitas::first();
        $expenseCategory = ExpenseCategory::first();
        $cashAccount = Akun::where('kategori_akun', 'Kas')->first();

        $this->assertNotNull($user, 'User not found');
        $this->assertNotNull($employee, 'Employee not found');
        $this->assertNotNull($entitas, 'Entitas not found');
        $this->assertNotNull($expenseCategory, 'Expense category not found');
        $this->assertNotNull($cashAccount, 'Cash account not found');

        // Act as user
        $this->actingAs($user);

        // 1. Create expense request
        $expenseRequest = ExpenseRequest::create([
            'request_number' => 'ER-TEST-' . time(),
            'request_date' => now()->toDateString(),
            'employee_id' => $employee->id,
            'entitas_id' => $entitas->id,
            'expense_type' => 'Petty_Cash',
            'total_amount' => 0, // Will be calculated
            'status' => 'Draft',
            'purpose' => 'Test expense request',
            'notes' => 'Testing workflow',
            'requested_by' => $user->id,
        ]);

        $this->assertInstanceOf(ExpenseRequest::class, $expenseRequest);
        $this->assertEquals('Draft', $expenseRequest->status);
        $this->assertEquals(0, $expenseRequest->total_amount);

        // 2. Add expense request items
        $expenseItem = ExpenseRequestItem::create([
            'expense_request_id' => $expenseRequest->id,
            'expense_category_id' => $expenseCategory->id,
            'description' => 'Test expense item',
            'amount' => 100000,
            'expense_date' => now()->toDateString(),
            'account_id' => $expenseCategory->default_account_id,
        ]);

        $this->assertInstanceOf(ExpenseRequestItem::class, $expenseItem);

        // Refresh expense request to get updated total
        $expenseRequest->refresh();
        $this->assertEquals(100000, $expenseRequest->total_amount);

        // 3. Submit expense request
        $expenseRequest->submit();
        $this->assertEquals('Submitted', $expenseRequest->status);

        // 4. Approve expense request with payment method
        $approvalData = [
            'payment_method' => 'Cash',
            'cash_account_id' => $cashAccount->id,
            'approval_notes' => 'Approved for testing',
        ];

        $expenseRequest->approve($approvalData);
        $this->assertEquals('Approved', $expenseRequest->status);
        $this->assertNotNull($expenseRequest->approved_by);
        $this->assertNotNull($expenseRequest->approved_at);
        $this->assertEquals($approvalData, $expenseRequest->approval_data);

        // 5. Check if journal entries were created
        $journal = Journal::where('source_type', 'ExpenseRequest')
            ->where('source_id', $expenseRequest->id)
            ->first();

        $this->assertNotNull($journal, 'Journal entry should be created');
        $this->assertEquals('Posted', $journal->status);

        // Check journal entries
        $journalEntries = $journal->journalEntries;
        $this->assertGreaterThan(0, $journalEntries->count());

        // Check debit entry (expense account)
        $debitEntry = $journalEntries->where('debit', '>', 0)->first();
        $this->assertNotNull($debitEntry);
        $this->assertEquals(100000, $debitEntry->debit);
        $this->assertEquals($expenseCategory->default_account_id, $debitEntry->account_id);

        // Check credit entry (cash account)
        $creditEntry = $journalEntries->where('credit', '>', 0)->first();
        $this->assertNotNull($creditEntry);
        $this->assertEquals(100000, $creditEntry->credit);
        $this->assertEquals($cashAccount->id, $creditEntry->account_id);

        // Verify journal is balanced
        $totalDebit = $journalEntries->sum('debit');
        $totalCredit = $journalEntries->sum('credit');
        $this->assertEquals($totalDebit, $totalCredit);

        echo "✅ Expense Request Workflow Test Passed!\n";
        echo "- Expense Request Created: {$expenseRequest->request_number}\n";
        echo "- Total Amount: Rp " . number_format($expenseRequest->total_amount) . "\n";
        echo "- Status: {$expenseRequest->status}\n";
        echo "- Journal Created: {$journal->journal_number}\n";
        echo "- Journal Status: {$journal->status}\n";
        echo "- Total Debit: Rp " . number_format($totalDebit) . "\n";
        echo "- Total Credit: Rp " . number_format($totalCredit) . "\n";
    }
}
