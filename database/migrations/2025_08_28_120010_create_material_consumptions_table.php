<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('material_consumptions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('production_order_id')->constrained('production_orders')->onDelete('cascade');
            $table->foreignId('production_stage_id')->nullable()->constrained('production_stages')->onDelete('set null');
            $table->foreignId('product_id')->constrained('products'); // Material consumed
            $table->foreignId('warehouse_id')->constrained('warehouses');
            $table->decimal('quantity_consumed', 10, 4);
            $table->string('unit', 20);
            $table->decimal('unit_cost', 15, 2);
            $table->decimal('total_cost', 15, 2);
            $table->datetime('consumption_date');
            $table->foreignId('consumed_by')->constrained('users'); // Who recorded the consumption
            $table->enum('consumption_type', ['normal', 'waste', 'rework', 'adjustment'])->default('normal');
            $table->text('reason')->nullable(); // Reason for consumption, especially for waste
            $table->string('batch_number')->nullable();
            $table->string('lot_number')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['production_order_id', 'consumption_date']);
            $table->index(['product_id', 'consumption_date']);
            $table->index(['warehouse_id', 'consumption_date']);
            $table->index(['consumption_type', 'consumption_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('material_consumptions');
    }
};
