<?php

namespace App\Filament\Setting\Resources\EventManagerResource\Pages;

use App\Filament\Setting\Resources\EventManagerResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListEventManagers extends ListRecords
{
    protected static string $resource = EventManagerResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
