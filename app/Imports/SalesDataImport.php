<?php

namespace App\Imports;

use App\Models\SalesTransaction;
use App\Models\SaleItem;
use App\Models\Product;
use App\Models\InventoryStock;
use App\Services\JournalingService;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class SalesDataImport implements ToModel, WithHeadingRow, WithValidation, WithBatchInserts, WithChunkReading
{
    private $journalingService;
    private $processedTransactions = [];
    private $warehouseId;
    private $userId;
    private $results = [
        'transactions_created' => 0,
        'journals_created' => 0,
        'items_processed' => 0,
        'errors' => []
    ];

    public function __construct(int $warehouseId, int $userId)
    {
        $this->warehouseId = $warehouseId;
        $this->userId = $userId;
        $this->journalingService = new JournalingService();
    }

    public function getResults(): array
    {
        return $this->results;
    }

    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        try {
            DB::beginTransaction();

            // Check if transaction already exists
            $existingTransaction = SalesTransaction::where('transaction_code', $row['transaction_code'])->first();
            if ($existingTransaction) {
                Log::info('Transaction already exists, skipping', ['transaction_code' => $row['transaction_code']]);
                DB::rollBack();
                return null;
            }

            // Find or create product
            $product = Product::where('sku', $row['product_code'])->first();
            if (!$product) {
                Log::warning('Product not found', ['product_code' => $row['product_code']]);
                DB::rollBack();
                return null;
            }

            // Check inventory stock in the specified warehouse
            $inventoryStock = InventoryStock::where('product_id', $product->id)
                ->where('warehouse_id', $this->warehouseId)
                ->first();

            if (!$inventoryStock || $inventoryStock->quantity < $row['quantity']) {
                $error = 'Insufficient inventory in warehouse';
                Log::warning($error, [
                    'product_code' => $row['product_code'],
                    'warehouse_id' => $this->warehouseId,
                    'required' => $row['quantity'],
                    'available' => $inventoryStock ? $inventoryStock->quantity : 0
                ]);
                $this->results['errors'][] = $error . ' for product: ' . $row['product_code'];
                DB::rollBack();
                return null;
            }

            // Create or update sales transaction
            $transactionCode = $row['transaction_code'];
            $salesTransaction = null;

            if (!isset($this->processedTransactions[$transactionCode])) {
                // Create new transaction
                $salesTransaction = SalesTransaction::create([
                    'transaction_code' => $transactionCode,
                    'transaction_date' => Carbon::parse($row['transaction_date'])->toDateString(),
                    'customer_name' => $row['customer_name'] ?? 'Walk-in Customer',
                    'warehouse_id' => $this->warehouseId,
                    'payment_method' => $row['payment_method'] ?? 'Cash',
                    'subtotal' => 0, // Will be calculated
                    'tax_amount' => $row['tax_amount'] ?? 0,
                    'discount_amount' => $row['discount_amount'] ?? 0,
                    'total_amount' => 0, // Will be calculated
                    'notes' => $row['notes'] ?? null,
                    'status' => 'Completed',
                    'created_by' => $this->userId,
                ]);

                $this->processedTransactions[$transactionCode] = $salesTransaction;
                $this->results['transactions_created']++;
            } else {
                $salesTransaction = $this->processedTransactions[$transactionCode];
            }

            // Create sale item
            $unitPrice = $row['unit_price'] ?? $product->selling_price ?? 0;
            $unitCost = $row['unit_cost'] ?? $product->unit_cost ?? $inventoryStock->average_cost ?? 0;
            $quantity = $row['quantity'];

            $saleItem = SaleItem::create([
                'sales_transaction_id' => $salesTransaction->id,
                'product_id' => $product->id,
                'quantity' => $quantity,
                'unit_price' => $unitPrice,
                'unit_cost' => $unitCost,
                'total_price' => $quantity * $unitPrice,
                'total_cost' => $quantity * $unitCost,
            ]);

            // Update inventory stock
            $inventoryStock->quantity -= $quantity;
            $inventoryStock->updateTotalValue();

            $this->results['items_processed']++;

            // Recalculate transaction totals
            $this->recalculateTransactionTotals($salesTransaction);

            // Post to journal (only once per transaction - when it's the first item)
            $itemCount = $salesTransaction->saleItems()->count();
            if ($itemCount === 1) {
                try {
                    $this->journalingService->postTransaction('Sale', $salesTransaction);
                    $this->results['journals_created']++;
                } catch (\Exception $e) {
                    Log::warning('Failed to create journal for transaction', [
                        'transaction_code' => $salesTransaction->transaction_code,
                        'error' => $e->getMessage()
                    ]);
                    // Continue processing even if journal creation fails
                }
            }

            DB::commit();
            return $saleItem;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error importing sales data', [
                'row' => $row,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Recalculate transaction totals
     */
    private function recalculateTransactionTotals(SalesTransaction $transaction)
    {
        $subtotal = $transaction->saleItems()->sum('total_price');
        $totalAmount = $subtotal + $transaction->tax_amount - $transaction->discount_amount;

        $transaction->update([
            'subtotal' => $subtotal,
            'total_amount' => $totalAmount,
        ]);
    }

    /**
     * Validation rules
     */
    public function rules(): array
    {
        return [
            'transaction_code' => 'required|string',
            'transaction_date' => 'required|date',
            'product_code' => 'required|string|exists:produk,kode',
            'quantity' => 'required|numeric|min:1',
            'unit_price' => 'nullable|numeric|min:0',
            'unit_cost' => 'nullable|numeric|min:0',
            'payment_method' => 'nullable|string|in:Cash,Credit,Transfer,Debit',
            'customer_name' => 'nullable|string|max:255',
            'tax_amount' => 'nullable|numeric|min:0',
            'discount_amount' => 'nullable|numeric|min:0',
        ];
    }

    /**
     * Custom validation messages
     */
    public function customValidationMessages()
    {
        return [
            'transaction_code.required' => 'Kode transaksi wajib diisi',
            'transaction_date.required' => 'Tanggal transaksi wajib diisi',
            'transaction_date.date' => 'Format tanggal tidak valid',
            'product_code.required' => 'Kode produk wajib diisi',
            'product_code.exists' => 'Produk dengan kode ini tidak ditemukan',
            'quantity.required' => 'Jumlah wajib diisi',
            'quantity.min' => 'Jumlah minimal 1',
            'unit_price.min' => 'Harga satuan tidak boleh negatif',
            'unit_cost.min' => 'Harga pokok tidak boleh negatif',
            'payment_method.in' => 'Metode pembayaran tidak valid',
        ];
    }

    /**
     * Batch size for processing
     */
    public function batchSize(): int
    {
        return 100;
    }

    /**
     * Chunk size for reading
     */
    public function chunkSize(): int
    {
        return 100;
    }
}
