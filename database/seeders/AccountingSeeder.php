<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Akun;
use App\Models\ProdukKategori;
use App\Models\Satuan;
use App\Models\Product;
use App\Models\Inventory;

class AccountingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Seed Chart of Accounts (COA) - Data lengkap sesuai permintaan user
        $accounts = [
            // BANK AND CASH
            ['kode_akun' => '********', 'nama_akun' => 'Cash', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'Petty Cash', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'Cash in Hand', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'Bank', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'Bank', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],

            // CURRENT ASSETS
            ['kode_akun' => '********', 'nama_akun' => 'Outstanding Receipts', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => true],
            ['kode_akun' => '********', 'nama_akun' => 'Outstanding Payments', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => true],
            ['kode_akun' => '********', 'nama_akun' => 'Bank Suspense Account', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'Outstanding Receipts', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => true],
            ['kode_akun' => '********', 'nama_akun' => 'Outstanding Payments', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => true],
            ['kode_akun' => '********', 'nama_akun' => 'Employee Liabilities', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => true],
            ['kode_akun' => '********', 'nama_akun' => 'Other Inventory', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'Liquidity Transfer', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => true],

            // RECEIVABLE
            ['kode_akun' => '********', 'nama_akun' => 'Account Receivable', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => true],
            ['kode_akun' => '********', 'nama_akun' => 'Account Receivable (PoS)', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => true],

            // PREPAYMENTS
            ['kode_akun' => '********', 'nama_akun' => 'Building Rent', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'Prepaid Insurance', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'Prepaid Advertisement-Free', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'Prepaid Tax PPh 21', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'Prepaid Tax Pph 22', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '11510030', 'nama_akun' => 'Prepaid Tax Pph 23', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '11510040', 'nama_akun' => 'Prepaid Tax Pph 25', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '11510050', 'nama_akun' => 'Prepaid Tax Pph 28A', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '11510060', 'nama_akun' => 'Prepaid Tax 4 (2)', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '11800000', 'nama_akun' => 'Down Payment', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '12281010', 'nama_akun' => 'Accumulation Building Depreciation', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '12281020', 'nama_akun' => 'Accumulation Vehicle Depreciation', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '12281030', 'nama_akun' => 'Accumulation Office Supplies Depreciation', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],

            // FIXED ASSETS
            ['kode_akun' => '12210010', 'nama_akun' => 'Office Building', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'Vehicle', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'Office Supplies', 'kategori_akun' => 'Aset', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],

            // CURRENT LIABILITIES
            ['kode_akun' => '********', 'nama_akun' => 'Bank Suspense', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => true],
            ['kode_akun' => '********', 'nama_akun' => 'Shareholder Deposit', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'Third-Party Deposit', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'Salary Deposit', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'Tax Payable Pph 21', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'Tax Payable Pph 23', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '21210030', 'nama_akun' => 'Tax Payable Pph 25', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '21210040', 'nama_akun' => 'Tax Payable 4 (2)', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '21210050', 'nama_akun' => 'Tax Payable Pph 29', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'VAT Purchase', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'VAT Sales', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'Bank Loan', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'Leasing Deposit', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'Accrued Payable Electricity', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'Accrued Payable Jamsostek', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'Accrued Payable Water', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'Accrued Payable Telp & Internet', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'Accrued Payable Security Management', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'Accrued Payable Bank', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'Accrued Payable PBB', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'Accrued Payable Business License', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'Accrued Payable Insurance', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'Accrued Payable Education', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'Accrued Payable Health Insurance/BPJS', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '28110010', 'nama_akun' => 'Advance Sales', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '28110020', 'nama_akun' => 'Customer Deposit', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '29000000', 'nama_akun' => 'Interim Stock', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],

            // PAYABLE
            ['kode_akun' => '21100010', 'nama_akun' => 'Trade Receivable', 'kategori_akun' => 'Kewajiban', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => true],

            // EQUITY
            ['kode_akun' => '31100010', 'nama_akun' => 'Authorized Capital', 'kategori_akun' => 'Ekuitas', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '31100020', 'nama_akun' => 'Paid Capital', 'kategori_akun' => 'Ekuitas', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '31100030', 'nama_akun' => 'Unpaid Capital', 'kategori_akun' => 'Ekuitas', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '31100040', 'nama_akun' => 'Prive (Personal Retrieval)', 'kategori_akun' => 'Ekuitas', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '31210010', 'nama_akun' => 'Capital Reserves', 'kategori_akun' => 'Ekuitas', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '31510010', 'nama_akun' => 'Past Profit & Loss', 'kategori_akun' => 'Ekuitas', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '31510020', 'nama_akun' => 'Ongoing Profit & Loss', 'kategori_akun' => 'Ekuitas', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '39000000', 'nama_akun' => 'Historical Balance', 'kategori_akun' => 'Ekuitas', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => true],
            ['kode_akun' => '99999900', 'nama_akun' => 'Undistributed Profits/Losses', 'kategori_akun' => 'Ekuitas', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],

            // INCOME
            ['kode_akun' => '41000010', 'nama_akun' => 'Sales', 'kategori_akun' => 'Pendapatan', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '42000060', 'nama_akun' => 'Sales Refund', 'kategori_akun' => 'Pendapatan', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '42000070', 'nama_akun' => 'Sales Discount', 'kategori_akun' => 'Pendapatan', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '99900002', 'nama_akun' => 'Cash Difference Gain', 'kategori_akun' => 'Pendapatan', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],

            // OTHER INCOME
            ['kode_akun' => '81100010', 'nama_akun' => 'Interest Income', 'kategori_akun' => 'Pendapatan', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '81100020', 'nama_akun' => 'Deposit Income', 'kategori_akun' => 'Pendapatan', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '81100030', 'nama_akun' => 'Foreign Exchange Gain', 'kategori_akun' => 'Pendapatan', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '81100040', 'nama_akun' => 'Other Income', 'kategori_akun' => 'Pendapatan', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '81100050', 'nama_akun' => 'Gain on Sale of Fixed Assets', 'kategori_akun' => 'Pendapatan', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '99900004', 'nama_akun' => 'Cash Discount Gain', 'kategori_akun' => 'Pendapatan', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '99900005', 'nama_akun' => 'Cash Difference Gain', 'kategori_akun' => 'Pendapatan', 'tipe_akun' => 'Kredit', 'saldo_awal' => 0, 'allow_reconciliation' => false],

            // COST OF REVENUE
            ['kode_akun' => '51000010', 'nama_akun' => 'Cost of Goods Sold', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],

            // EXPENSES
            ['kode_akun' => '54321', 'nama_akun' => 'deferred outcome', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '61100010', 'nama_akun' => 'Employee Salary', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '61100020', 'nama_akun' => 'Employee Bonus / Benefits', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '61100030', 'nama_akun' => 'Employee Overtime Pay', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '61100100', 'nama_akun' => 'Pph 21 Benefit', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '63110060', 'nama_akun' => 'Phone', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '63110080', 'nama_akun' => 'Electricity', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '63110100', 'nama_akun' => 'Research & Development', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '63110120', 'nama_akun' => 'Office Equipment', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '63110140', 'nama_akun' => 'Other Necessities', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'Post Necessities', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'Licensing Fees', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'Bank Administration Fees', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'Consultant Fees', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'Rental Costs', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'Insurance Costs', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'Building Maintenance Costs', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'Taxes', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '65110080', 'nama_akun' => 'Asset Maintenance Costs', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '65110090', 'nama_akun' => 'Shipping Costs', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '66110010', 'nama_akun' => 'Vehicle Fuel', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '66110020', 'nama_akun' => 'Vehicle Service', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '66110030', 'nama_akun' => 'Vehicle Parking & Toll Fee', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '66110040', 'nama_akun' => 'Vehicle Taxes', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '66110050', 'nama_akun' => 'Vehicle Insurance', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '69000000', 'nama_akun' => 'Other Expenses', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '91100010', 'nama_akun' => 'Interest Expense', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '91100020', 'nama_akun' => 'Foreign Exchange Loss', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '91100030', 'nama_akun' => 'Loss on Sale of Fixed Assets', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '99900001', 'nama_akun' => 'Cash Difference Loss', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '99900003', 'nama_akun' => 'Cash Discount Loss', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '99900006', 'nama_akun' => 'Cash Difference Loss', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],

            // DEPRECIATION
            ['kode_akun' => '67100010', 'nama_akun' => 'Office Building', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'Vehicle', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
            ['kode_akun' => '********', 'nama_akun' => 'Office Supplies', 'kategori_akun' => 'Beban', 'tipe_akun' => 'Debit', 'saldo_awal' => 0, 'allow_reconciliation' => false],
        ];

        foreach ($accounts as $account) {
            Akun::create($account);
        }

        // Seed Product Categories
        $categories = [
            ['nama' => 'Elektronik', 'deskripsi' => 'Produk elektronik dan gadget', 'created_by' => 1],
            ['nama' => 'Fashion', 'deskripsi' => 'Pakaian dan aksesoris', 'created_by' => 1],
            ['nama' => 'Makanan & Minuman', 'deskripsi' => 'Produk makanan dan minuman', 'created_by' => 1],
            ['nama' => 'Kesehatan', 'deskripsi' => 'Produk kesehatan dan kecantikan', 'created_by' => 1],
            ['nama' => 'Rumah Tangga', 'deskripsi' => 'Peralatan rumah tangga', 'created_by' => 1],
        ];

        foreach ($categories as $category) {
            ProdukKategori::create($category);
        }

        // Seed Units
        $units = [
            ['nama_satuan' => 'Pcs', 'deskripsi_satuan' => 'Pieces/Unit', 'created_by' => 1],
            ['nama_satuan' => 'Kg', 'deskripsi_satuan' => 'Kilogram', 'created_by' => 1],
            ['nama_satuan' => 'Liter', 'deskripsi_satuan' => 'Liter', 'created_by' => 1],
            ['nama_satuan' => 'Box', 'deskripsi_satuan' => 'Box/Kotak', 'created_by' => 1],
            ['nama_satuan' => 'Pack', 'deskripsi_satuan' => 'Pack/Kemasan', 'created_by' => 1],
        ];

        foreach ($units as $unit) {
            Satuan::create($unit);
        }
    }
}
