<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Bom;
use App\Models\BomItem;
use App\Models\ProductionPlan;
use App\Models\ProductionOrder;
use App\Models\QualityControlPoint;
use App\Models\QualityInspection;
use App\Models\Product;
use App\Models\Warehouse;
use App\Models\User;

class ManufacturingSeeder extends Seeder
{
    public function run(): void
    {
        // Get existing data
        $users = User::all();
        $warehouses = Warehouse::all();
        $products = Product::all();

        if ($users->isEmpty() || $warehouses->isEmpty() || $products->isEmpty()) {
            $this->command->warn('Please run UserSeeder, WarehouseSeeder, and ProductSeeder first');
            return;
        }

        $this->command->info('Seeding Manufacturing data...');

        // Create Quality Control Points
        $this->createQualityControlPoints($users->first());

        // Create BOMs
        $boms = $this->createBoms($products, $users->first());

        // Create Production Plans
        $plans = $this->createProductionPlans($warehouses->first(), $users->first());

        // Create Production Orders
        $this->createProductionOrders($boms, $plans, $warehouses->first(), $users);

        $this->command->info('Manufacturing data seeded successfully!');
    }

    private function createQualityControlPoints($user): void
    {
        $qcPoints = [
            [
                'name' => 'Incoming Material Inspection',
                'description' => 'Quality check for incoming raw materials',
                'type' => 'incoming',
                'is_mandatory' => true,
                'sequence' => 1,
                'min_acceptable_value' => 95.0,
                'max_acceptable_value' => 100.0,
                'unit_of_measure' => '%',
                'instructions' => 'Check material quality, dimensions, and specifications',
                'created_by' => $user->id,
            ],
            [
                'name' => 'Production Process Check',
                'description' => 'Quality check during production process',
                'type' => 'in_process',
                'is_mandatory' => true,
                'sequence' => 2,
                'min_acceptable_value' => 98.0,
                'max_acceptable_value' => 100.0,
                'unit_of_measure' => '%',
                'instructions' => 'Monitor production quality and consistency',
                'created_by' => $user->id,
            ],
            [
                'name' => 'Final Product Inspection',
                'description' => 'Final quality check before packaging',
                'type' => 'final',
                'is_mandatory' => true,
                'sequence' => 3,
                'min_acceptable_value' => 99.0,
                'max_acceptable_value' => 100.0,
                'unit_of_measure' => '%',
                'instructions' => 'Complete final inspection checklist',
                'created_by' => $user->id,
            ],
        ];

        foreach ($qcPoints as $qcPoint) {
            QualityControlPoint::create($qcPoint);
        }
    }

    private function createBoms($products, $user): array
    {
        $boms = [];
        $finishedProducts = $products->take(3); // Take first 3 products as finished goods
        $rawMaterials = $products->skip(3)->take(5); // Next 5 as raw materials

        foreach ($finishedProducts as $index => $product) {
            $bom = Bom::create([
                'name' => 'BOM for ' . $product->name,
                'description' => 'Bill of Materials for manufacturing ' . $product->name,
                'product_id' => $product->id,
                'version' => '1.0',
                'status' => 'active',
                'quantity' => 1,
                'unit' => 'pcs',
                'labor_cost_per_unit' => 50000 + ($index * 10000),
                'overhead_cost_per_unit' => 25000 + ($index * 5000),
                'lead_time_days' => 3 + $index,
                'created_by' => $user->id,
                'approved_by' => $user->id,
                'approved_at' => now(),
            ]);

            // Create BOM Items
            foreach ($rawMaterials->take(3) as $materialIndex => $material) {
                BomItem::create([
                    'bom_id' => $bom->id,
                    'component_product_id' => $material->id,
                    'quantity_required' => 2 + $materialIndex,
                    'unit' => $material->unit ?? 'pcs',
                    'unit_cost' => $material->cost ?? 10000,
                    'waste_percentage' => 5.0,
                    'is_critical' => $materialIndex === 0,
                    'sequence' => $materialIndex + 1,
                ]);
            }

            // Calculate total costs
            $bom->calculateTotalCost();
            $boms[] = $bom;
        }

        return $boms;
    }

    private function createProductionPlans($warehouse, $user): array
    {
        $plans = [];

        for ($i = 1; $i <= 3; $i++) {
            $startDate = now()->addDays($i * 7);
            $endDate = $startDate->copy()->addDays(14);

            $plan = ProductionPlan::create([
                'name' => 'Production Plan Q' . ceil($i / 3) . ' Week ' . $i,
                'description' => 'Quarterly production plan for week ' . $i,
                'plan_start_date' => $startDate,
                'plan_end_date' => $endDate,
                'status' => $i === 1 ? 'in_progress' : ($i === 2 ? 'approved' : 'draft'),
                'priority' => $i === 1 ? 'high' : 'normal',
                'warehouse_id' => $warehouse->id,
                'total_planned_quantity' => 100 * $i,
                'total_planned_cost' => 5000000 * $i,
                'total_planned_hours' => 160 * $i,
                'created_by' => $user->id,
                'approved_by' => $i <= 2 ? $user->id : null,
                'approved_at' => $i <= 2 ? now() : null,
                'started_at' => $i === 1 ? now() : null,
            ]);

            $plans[] = $plan;
        }

        return $plans;
    }

    private function createProductionOrders($boms, $plans, $warehouse, $users): void
    {
        foreach ($plans as $planIndex => $plan) {
            foreach ($boms as $bomIndex => $bom) {
                $startDate = $plan->plan_start_date->addDays($bomIndex * 2);
                $endDate = $startDate->copy()->addDays(5);

                $order = ProductionOrder::create([
                    'production_plan_id' => $plan->id,
                    'bom_id' => $bom->id,
                    'product_id' => $bom->product_id,
                    'warehouse_id' => $warehouse->id,
                    'planned_quantity' => 20 + ($bomIndex * 5),
                    'actual_quantity' => $planIndex === 0 ? 18 + ($bomIndex * 5) : 0,
                    'unit' => 'pcs',
                    'status' => $planIndex === 0 ? 'completed' : ($planIndex === 1 ? 'in_progress' : 'planned'),
                    'priority' => $bomIndex === 0 ? 'high' : 'normal',
                    'planned_start_date' => $startDate,
                    'planned_end_date' => $endDate,
                    'actual_start_date' => $planIndex <= 1 ? $startDate : null,
                    'actual_end_date' => $planIndex === 0 ? $endDate->subDay() : null,
                    'planned_material_cost' => $bom->total_material_cost * (20 + ($bomIndex * 5)),
                    'planned_labor_cost' => $bom->labor_cost_per_unit * (20 + ($bomIndex * 5)),
                    'planned_overhead_cost' => $bom->overhead_cost_per_unit * (20 + ($bomIndex * 5)),
                    'actual_material_cost' => $planIndex === 0 ? $bom->total_material_cost * (18 + ($bomIndex * 5)) : 0,
                    'actual_labor_cost' => $planIndex === 0 ? $bom->labor_cost_per_unit * (18 + ($bomIndex * 5)) : 0,
                    'actual_overhead_cost' => $planIndex === 0 ? $bom->overhead_cost_per_unit * (18 + ($bomIndex * 5)) : 0,
                    'planned_duration_hours' => 40 + ($bomIndex * 8),
                    'actual_duration_hours' => $planIndex === 0 ? 38 + ($bomIndex * 8) : 0,
                    'yield_percentage' => $planIndex === 0 ? 95.5 + $bomIndex : 100,
                    'assigned_to' => $users->random()->id,
                    'created_by' => $users->first()->id,
                ]);

                // Calculate total costs
                $order->update([
                    'total_planned_cost' => $order->planned_material_cost + $order->planned_labor_cost + $order->planned_overhead_cost,
                    'total_actual_cost' => $order->actual_material_cost + $order->actual_labor_cost + $order->actual_overhead_cost,
                ]);

                // Generate material requirements and stages for released/in-progress orders
                if (in_array($order->status, ['released', 'in_progress', 'completed'])) {
                    $order->generateMaterialRequirements();
                    $order->generateProductionStages();
                }

                // Create quality inspections for completed orders
                if ($order->status === 'completed') {
                    $this->createQualityInspections($order, $users);
                }
            }
        }
    }

    private function createQualityInspections($order, $users): void
    {
        $qcPoints = QualityControlPoint::active()->get();

        foreach ($qcPoints as $qcPoint) {
            $inspection = QualityInspection::create([
                'production_order_id' => $order->id,
                'quality_control_point_id' => $qcPoint->id,
                'quantity_inspected' => $order->actual_quantity,
                'quantity_passed' => $order->actual_quantity - 1,
                'quantity_failed' => 0,
                'quantity_rework' => 1,
                'overall_result' => 'passed',
                'inspection_date' => $order->actual_end_date ?? now(),
                'inspector_id' => $users->random()->id,
                'notes' => 'Quality inspection completed successfully',
                'is_final_inspection' => $qcPoint->type === 'final',
            ]);
        }
    }
}
