<?php

namespace App\Filament\Accounting\Resources\BankReconciliationResource\Pages;

use App\Filament\Accounting\Resources\BankReconciliationResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditBankReconciliation extends EditRecord
{
    protected static string $resource = BankReconciliationResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make()
                ->label('Lihat Rekonsiliasi')
                ->icon('heroicon-o-eye'),
            Actions\Action::make('reconcile')
                ->label('Proses Rekonsiliasi')
                ->icon('heroicon-o-scale')
                ->color('info')
                ->visible(fn () => $this->record->status === 'draft' || $this->record->status === 'in_progress')
                ->url(fn () => static::getResource()::getUrl('reconcile', ['record' => $this->record])),
            Actions\DeleteAction::make()
                ->label('Hapus Rekonsiliasi')
                ->icon('heroicon-o-trash'),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getSavedNotificationTitle(): ?string
    {
        return 'Rekonsiliasi bank berhasil diperbarui';
    }
}
