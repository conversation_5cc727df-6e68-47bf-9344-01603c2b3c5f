<?php

namespace App\Filament\Accounting\Resources\TransactionCategoryResource\Pages;

use App\Filament\Accounting\Resources\TransactionCategoryResource;
use Filament\Resources\Pages\CreateRecord;

class CreateTransactionCategory extends CreateRecord
{
    protected static string $resource = TransactionCategoryResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    protected function getCreatedNotificationTitle(): ?string
    {
        return 'Kategori transaksi berhasil dibuat';
    }
}
