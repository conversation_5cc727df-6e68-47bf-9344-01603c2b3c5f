<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class NotificationSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'event_id',
        'user_id',
        'channel',
        'is_active',
    ];

    public function event(): BelongsTo
    {
        return $this->belongsTo(EventManager::class, 'event_id');
    }

    public function karyawan(): BelongsTo
    {
        return $this->belongsTo(Karyawan::class, 'user_id');
    }

    /**
     * <PERSON><PERSON>i dan mengembalikan semua pengaturan notifikasi yang aktif untuk sebuah event.
     *
     * @param string $eventName Nama event yang akan dicari.
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function findActiveRecipientsForEvent(string $eventName)
    {
        return static::with('karyawan')
            ->where('event_name', $eventName)
            ->where('is_active', true)
            ->get();
    }
}
