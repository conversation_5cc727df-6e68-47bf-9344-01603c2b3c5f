<?php

namespace App\Filament\Manufacturing\Resources\BomResource\RelationManagers;

use App\Models\Product;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class BomItemsRelationManager extends RelationManager
{
    protected static string $relationship = 'bomItems';

    protected static ?string $recordTitleAttribute = 'componentProduct.name';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\Select::make('component_product_id')
                            ->label('Component Product')
                            ->options(Product::all()->pluck('name', 'id'))
                            ->searchable()
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set) {
                                if ($state) {
                                    $product = Product::find($state);
                                    if ($product) {
                                        $set('unit', $product->unit ?? 'pcs');
                                        $set('unit_cost', $product->cost ?? 0);
                                    }
                                }
                            }),

                        Forms\Components\TextInput::make('sequence')
                            ->label('Sequence')
                            ->numeric()
                            ->default(0)
                            ->required(),
                    ]),

                Forms\Components\Grid::make(3)
                    ->schema([
                        Forms\Components\TextInput::make('quantity_required')
                            ->label('Quantity Required')
                            ->numeric()
                            ->step(0.0001)
                            ->required()
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                $unitCost = $get('unit_cost') ?? 0;
                                $set('total_cost', $state * $unitCost);
                            }),

                        Forms\Components\TextInput::make('unit')
                            ->required(),

                        Forms\Components\TextInput::make('waste_percentage')
                            ->label('Waste %')
                            ->numeric()
                            ->step(0.01)
                            ->default(0)
                            ->suffix('%'),
                    ]),

                Forms\Components\Grid::make(3)
                    ->schema([
                        Forms\Components\TextInput::make('unit_cost')
                            ->label('Unit Cost')
                            ->numeric()
                            ->prefix('Rp')
                            ->reactive()
                            ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                $quantity = $get('quantity_required') ?? 0;
                                $set('total_cost', $quantity * $state);
                            }),

                        Forms\Components\TextInput::make('total_cost')
                            ->label('Total Cost')
                            ->numeric()
                            ->prefix('Rp')
                            ->disabled()
                            ->dehydrated(),

                        Forms\Components\Toggle::make('is_critical')
                            ->label('Critical Component')
                            ->default(false),
                    ]),

                Forms\Components\Textarea::make('notes')
                    ->rows(2)
                    ->columnSpanFull(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('componentProduct.name')
            ->columns([
                Tables\Columns\TextColumn::make('sequence')
                    ->sortable(),

                Tables\Columns\TextColumn::make('componentProduct.sku')
                    ->label('Product Code')
                    ->searchable(),

                Tables\Columns\TextColumn::make('componentProduct.name')
                    ->label('Component')
                    ->searchable()
                    ->wrap(),

                Tables\Columns\TextColumn::make('quantity_required')
                    ->label('Qty Required')
                    ->numeric(decimalPlaces: 4),

                Tables\Columns\TextColumn::make('unit'),

                Tables\Columns\TextColumn::make('waste_percentage')
                    ->label('Waste %')
                    ->numeric(decimalPlaces: 2)
                    ->suffix('%'),

                Tables\Columns\TextColumn::make('unit_cost')
                    ->label('Unit Cost')
                    ->money('IDR'),

                Tables\Columns\TextColumn::make('total_cost')
                    ->label('Total Cost')
                    ->money('IDR'),

                Tables\Columns\IconColumn::make('is_critical')
                    ->label('Critical')
                    ->boolean(),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_critical')
                    ->label('Critical Components'),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->mutateFormDataUsing(function (array $data): array {
                        // Calculate actual quantity including waste
                        if (isset($data['waste_percentage']) && $data['waste_percentage'] > 0) {
                            $wasteMultiplier = 1 + ($data['waste_percentage'] / 100);
                            $data['actual_quantity_required'] = $data['quantity_required'] * $wasteMultiplier;
                        } else {
                            $data['actual_quantity_required'] = $data['quantity_required'];
                        }
                        return $data;
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('sequence');
    }
}
