<?php

namespace App\Filament\Manufacturing\Resources\MaterialConsumptionResource\Pages;

use App\Filament\Manufacturing\Resources\MaterialConsumptionResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditMaterialConsumption extends EditRecord
{
    protected static string $resource = MaterialConsumptionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\ViewAction::make(),
            Actions\DeleteAction::make(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
