<?php

namespace App\Services;

use App\Models\ExpenseRequest;
use App\Models\NotificationSetting;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Throwable;

class ExpenseRequestService
{
    public WhatsAppService $waService;

    public function __construct()
    {
        $this->waService = new WhatsAppService();
    }

    public function handleCreationNotification(ExpenseRequest $expenseRequest, string $event)
    {
        try {
            $notifications = NotificationSetting::where('event_id', $event)->get();

            foreach ($notifications as $key => $notif) {
                if ($notif->channel == "whatsapp") {
                    $this->waService->sendNotification($notif, [
                        "pengirim" => $expenseRequest->requestedBy->name,
                        "no_req" => $expenseRequest->request_number,
                        "judul" => $expenseRequest->ExpenseTypeLabel,
                        "jumlah" => $expenseRequest->FormattedTotalAmount,
                        "url" => route('filament.finance.resources.expense-requests.view', ["record" =>  $expenseRequest]),
                    ]);
                }
            }
        } catch (Throwable $e) {
            // 8. If any error occurs during the notification process, log it
            //    without crashing the main application flow.
            Log::error('Failed to send new expense request notification.', [
                'expense_request_id' => $expenseRequest->id,
                'error_message'      => $e->getMessage(),
                'trace'              => $e->getTraceAsString(), // Optional: for detailed debugging
            ]);
        }
    }

    public function handleRevisionNotification(ExpenseRequest $expenseRequest, string $event)
    {
        try {
            $notifications = NotificationSetting::where('event_id', $event)->get();

            foreach ($notifications as $key => $notif) {
                if ($notif->channel == "whatsapp") {
                    $this->waService->sendNotification($notif, [
                        "pengirim" => $expenseRequest->requestedBy->name,
                        "no_req" => $expenseRequest->request_number,
                        "judul" => $expenseRequest->ExpenseTypeLabel,
                        "jumlah" => $expenseRequest->FormattedTotalAmount,
                        "url" => route('filament.finance.resources.expense-requests.view', ["record" =>  $expenseRequest]),
                    ]);
                }
            }
        } catch (Throwable $e) {
            // 8. If any error occurs during the notification process, log it
            //    without crashing the main application flow.
            Log::error('Failed to send revision expense request notification.', [
                'expense_request_id' => $expenseRequest->id,
                'error_message'      => $e->getMessage(),
                'trace'              => $e->getTraceAsString(), // Optional: for detailed debugging
            ]);
        }
    }
}
