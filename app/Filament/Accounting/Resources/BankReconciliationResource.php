<?php

namespace App\Filament\Accounting\Resources;

use App\Filament\Accounting\Resources\BankReconciliationResource\Pages;
use App\Models\BankReconciliation;
use App\Models\Akun;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class BankReconciliationResource extends Resource
{
    protected static ?string $model = BankReconciliation::class;

    protected static ?string $navigationIcon = 'heroicon-o-scale';

    protected static ?string $navigationLabel = 'Rekonsiliasi Bank';

    protected static ?string $modelLabel = 'Rekonsiliasi Bank';

    protected static ?string $pluralModelLabel = 'Rekonsiliasi Bank';

    protected static ?string $navigationGroup = 'Rekonsiliasi';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Rekonsiliasi')
                    ->schema([
                        Forms\Components\TextInput::make('reconciliation_number')
                            ->label('Nomor Rekonsiliasi')
                            ->disabled()
                            ->dehydrated(false)
                            ->placeholder('Auto-generated'),
                        
                        Forms\Components\Select::make('account_id')
                            ->label('Akun Bank')
                            ->required()
                            ->options(Akun::where('allow_reconciliation', true)->pluck('nama_akun', 'id'))
                            ->searchable()
                            ->preload()
                            ->helperText('Hanya akun yang diizinkan untuk rekonsiliasi'),
                        
                        Forms\Components\DatePicker::make('reconciliation_date')
                            ->label('Tanggal Rekonsiliasi')
                            ->required()
                            ->default(now())
                            ->native(false),
                        
                        Forms\Components\DatePicker::make('statement_date')
                            ->label('Tanggal Statement')
                            ->required()
                            ->native(false),
                        
                        Forms\Components\TextInput::make('statement_reference')
                            ->label('Referensi Statement')
                            ->maxLength(255)
                            ->placeholder('Nomor atau referensi bank statement'),
                    ])
                    ->columns(2),
                
                Forms\Components\Section::make('Saldo')
                    ->schema([
                        Forms\Components\TextInput::make('opening_balance')
                            ->label('Saldo Awal')
                            ->required()
                            ->numeric()
                            ->prefix('Rp')
                            ->step(0.01)
                            ->default(0),
                        
                        Forms\Components\TextInput::make('closing_balance')
                            ->label('Saldo Akhir')
                            ->required()
                            ->numeric()
                            ->prefix('Rp')
                            ->step(0.01)
                            ->default(0),
                        
                        Forms\Components\Select::make('status')
                            ->label('Status')
                            ->required()
                            ->options([
                                'draft' => 'Draft',
                                'in_progress' => 'Dalam Proses',
                                'completed' => 'Selesai',
                                'cancelled' => 'Dibatalkan',
                            ])
                            ->default('draft')
                            ->native(false),
                        
                        Forms\Components\Textarea::make('notes')
                            ->label('Catatan')
                            ->maxLength(1000)
                            ->rows(3)
                            ->placeholder('Catatan tambahan untuk rekonsiliasi'),
                    ])
                    ->columns(2),
                
                Forms\Components\Hidden::make('created_by')
                    ->default(auth()->id()),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('reconciliation_number')
                    ->label('Nomor')
                    ->searchable()
                    ->sortable()
                    ->copyable(),
                
                Tables\Columns\TextColumn::make('account.nama_akun')
                    ->label('Akun Bank')
                    ->searchable()
                    ->sortable()
                    ->wrap(),
                
                Tables\Columns\TextColumn::make('reconciliation_date')
                    ->label('Tgl Rekonsiliasi')
                    ->date('d/m/Y')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('statement_date')
                    ->label('Tgl Statement')
                    ->date('d/m/Y')
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('opening_balance')
                    ->label('Saldo Awal')
                    ->money('IDR')
                    ->alignEnd(),
                
                Tables\Columns\TextColumn::make('closing_balance')
                    ->label('Saldo Akhir')
                    ->money('IDR')
                    ->alignEnd(),
                
                Tables\Columns\BadgeColumn::make('status')
                    ->label('Status')
                    ->formatStateUsing(fn (string $state): string => match ($state) {
                        'draft' => 'Draft',
                        'in_progress' => 'Dalam Proses',
                        'completed' => 'Selesai',
                        'cancelled' => 'Dibatalkan',
                        default => $state,
                    })
                    ->colors([
                        'secondary' => 'draft',
                        'warning' => 'in_progress',
                        'success' => 'completed',
                        'danger' => 'cancelled',
                    ]),
                
                Tables\Columns\TextColumn::make('matched_count')
                    ->label('Item Cocok')
                    ->getStateUsing(fn ($record) => $record->getMatchedCountAttribute())
                    ->badge()
                    ->color('success'),
                
                Tables\Columns\TextColumn::make('unmatched_count')
                    ->label('Belum Cocok')
                    ->getStateUsing(fn ($record) => $record->getUnmatchedCountAttribute())
                    ->badge()
                    ->color('warning'),
                
                Tables\Columns\TextColumn::make('reconciledBy.name')
                    ->label('Direkonsiliasi Oleh')
                    ->placeholder('Belum selesai')
                    ->toggleable(isToggledHiddenByDefault: true),
                
                Tables\Columns\TextColumn::make('reconciled_at')
                    ->label('Tgl Selesai')
                    ->dateTime('d/m/Y H:i')
                    ->placeholder('Belum selesai')
                    ->toggleable(isToggledHiddenByDefault: true),
                
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('account_id')
                    ->label('Akun Bank')
                    ->options(Akun::where('allow_reconciliation', true)->pluck('nama_akun', 'id'))
                    ->searchable(),
                
                Tables\Filters\SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        'draft' => 'Draft',
                        'in_progress' => 'Dalam Proses',
                        'completed' => 'Selesai',
                        'cancelled' => 'Dibatalkan',
                    ]),
                
                Tables\Filters\Filter::make('reconciliation_date')
                    ->form([
                        Forms\Components\DatePicker::make('from')
                            ->label('Dari Tanggal'),
                        Forms\Components\DatePicker::make('until')
                            ->label('Sampai Tanggal'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['from'],
                                fn (Builder $query, $date): Builder => $query->whereDate('reconciliation_date', '>=', $date),
                            )
                            ->when(
                                $data['until'],
                                fn (Builder $query, $date): Builder => $query->whereDate('reconciliation_date', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('reconcile')
                    ->label('Proses Rekonsiliasi')
                    ->icon('heroicon-o-scale')
                    ->color('info')
                    ->visible(fn ($record) => $record->status === 'draft' || $record->status === 'in_progress')
                    ->url(fn ($record) => static::getUrl('reconcile', ['record' => $record])),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBankReconciliations::route('/'),
            'create' => Pages\CreateBankReconciliation::route('/create'),
            'view' => Pages\ViewBankReconciliation::route('/{record}'),
            'edit' => Pages\EditBankReconciliation::route('/{record}/edit'),
            'reconcile' => Pages\ReconcileBankReconciliation::route('/{record}/reconcile'),
        ];
    }
}
